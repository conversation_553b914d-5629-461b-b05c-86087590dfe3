import { Quiz, QuizQuestion } from '../types/quiz';

// Genesis Chapter 1 Quiz - Example implementation
export const GENESIS_1_QUIZ: Quiz = {
  id: 'genesis-1-quiz',
  title: 'Genesis Chapter 1 Quiz',
  slug: 'genesis-1-quiz',
  description: 'Test your knowledge of Genesis chapter 1 with this comprehensive Bible quiz covering creation, <PERSON>\'s creative work, and the beginning of all things.',
  category: 'chapter',
  subcategory: 'genesis',
  questions: [
    {
      id: 'gen1-q1',
      questionType: 'multiple-choice',
      question: 'What did God create on the first day?',
      answers: [
        { id: 'a', text: 'The sun and moon', isCorrect: false },
        { id: 'b', text: 'Light and darkness', isCorrect: true },
        { id: 'c', text: 'The earth and sea', isCorrect: false },
        { id: 'd', text: 'Plants and trees', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'On the first day, God created light and separated it from darkness, calling the light "day" and the darkness "night."',
      bibleReference: 'Genesis 1:3-5',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q2',
      questionType: 'multiple-choice',
      question: 'What did God create on the second day?',
      answers: [
        { id: 'a', text: 'Dry land and seas', isCorrect: false },
        { id: 'b', text: 'The firmament (sky)', isCorrect: true },
        { id: 'c', text: 'Sun, moon, and stars', isCorrect: false },
        { id: 'd', text: 'Birds and fish', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'On the second day, God made the firmament (expanse/sky) to separate the waters above from the waters below.',
      bibleReference: 'Genesis 1:6-8',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q3',
      questionType: 'true-false',
      question: 'God created plants and trees on the third day.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'Yes, on the third day God created dry land and vegetation, including plants and trees.',
      bibleReference: 'Genesis 1:9-13',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q4',
      questionType: 'multiple-choice',
      question: 'What did God create on the fourth day?',
      answers: [
        { id: 'a', text: 'Animals and birds', isCorrect: false },
        { id: 'b', text: 'Man and woman', isCorrect: false },
        { id: 'c', text: 'Sun, moon, and stars', isCorrect: true },
        { id: 'd', text: 'Fish and sea creatures', isCorrect: false }
      ],
      correctAnswerId: 'c',
      explanation: 'On the fourth day, God created the sun, moon, and stars to give light and mark seasons, days, and years.',
      bibleReference: 'Genesis 1:14-19',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q5',
      questionType: 'multiple-choice',
      question: 'What did God create on the fifth day?',
      answers: [
        { id: 'a', text: 'Land animals', isCorrect: false },
        { id: 'b', text: 'Birds and sea creatures', isCorrect: true },
        { id: 'c', text: 'Plants and trees', isCorrect: false },
        { id: 'd', text: 'Man and woman', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'On the fifth day, God created birds to fly in the sky and fish and sea creatures to fill the waters.',
      bibleReference: 'Genesis 1:20-23',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q6',
      questionType: 'multiple-choice',
      question: 'In whose image did God create man?',
      answers: [
        { id: 'a', text: 'In the image of angels', isCorrect: false },
        { id: 'b', text: 'In His own image', isCorrect: true },
        { id: 'c', text: 'In the image of animals', isCorrect: false },
        { id: 'd', text: 'In no particular image', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God created man in His own image, in the image of God He created him; male and female He created them.',
      bibleReference: 'Genesis 1:27',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q7',
      questionType: 'true-false',
      question: 'God rested on the seventh day because He was tired.',
      answers: [
        { id: 'true', text: 'True', isCorrect: false },
        { id: 'false', text: 'False', isCorrect: true }
      ],
      correctAnswerId: 'false',
      explanation: 'God rested not because He was tired, but to establish the pattern of rest and to sanctify the seventh day.',
      bibleReference: 'Genesis 2:2-3',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen1-q8',
      questionType: 'multiple-choice',
      question: 'What was God\'s assessment of His creation?',
      answers: [
        { id: 'a', text: 'It was acceptable', isCorrect: false },
        { id: 'b', text: 'It was very good', isCorrect: true },
        { id: 'c', text: 'It was perfect', isCorrect: false },
        { id: 'd', text: 'It needed improvement', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God saw everything that He had made, and indeed it was very good.',
      bibleReference: 'Genesis 1:31',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q9',
      questionType: 'fill-blank',
      question: 'In the beginning God created the _______ and the _______.',
      answers: [
        { id: 'answer', text: 'heavens, earth', isCorrect: true }
      ],
      correctAnswerId: 'answer',
      explanation: 'The very first verse of the Bible states: "In the beginning God created the heavens and the earth."',
      bibleReference: 'Genesis 1:1',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q10',
      questionType: 'multiple-choice',
      question: 'What was the first thing God said in creation?',
      answers: [
        { id: 'a', text: 'Let there be light', isCorrect: true },
        { id: 'b', text: 'Let there be land', isCorrect: false },
        { id: 'c', text: 'Let there be water', isCorrect: false },
        { id: 'd', text: 'Let there be life', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'The first recorded words of God in creation were "Let there be light," and there was light.',
      bibleReference: 'Genesis 1:3',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q11',
      questionType: 'true-false',
      question: 'The Spirit of God was hovering over the face of the waters.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'Genesis 1:2 tells us that the Spirit of God was hovering over the face of the waters.',
      bibleReference: 'Genesis 1:2',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q12',
      questionType: 'multiple-choice',
      question: 'What dominion did God give to mankind?',
      answers: [
        { id: 'a', text: 'Over the fish, birds, and every living thing', isCorrect: true },
        { id: 'b', text: 'Over the angels only', isCorrect: false },
        { id: 'c', text: 'Over the plants only', isCorrect: false },
        { id: 'd', text: 'Over nothing', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'God blessed them and said to them, "Be fruitful and increase in number; fill the earth and subdue it. Rule over the fish in the sea and the birds in the sky and over every living creature that moves on the ground."',
      bibleReference: 'Genesis 1:28',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen1-q13',
      questionType: 'multiple-choice',
      question: 'How many days did it take God to create everything?',
      answers: [
        { id: 'a', text: 'Five days', isCorrect: false },
        { id: 'b', text: 'Six days', isCorrect: true },
        { id: 'c', text: 'Seven days', isCorrect: false },
        { id: 'd', text: 'Eight days', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God created everything in six days and rested on the seventh day.',
      bibleReference: 'Genesis 1:31, 2:2',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q14',
      questionType: 'true-false',
      question: 'God created both male and female on the sixth day.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'On the sixth day, God created mankind in His image; male and female He created them.',
      bibleReference: 'Genesis 1:27',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen1-q15',
      questionType: 'multiple-choice',
      question: 'What did God give to humans and animals for food?',
      answers: [
        { id: 'a', text: 'Only meat', isCorrect: false },
        { id: 'b', text: 'Plants and herbs', isCorrect: true },
        { id: 'c', text: 'Only fruits', isCorrect: false },
        { id: 'd', text: 'Nothing specific', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God gave every green plant for food to both humans and animals in the original creation.',
      bibleReference: 'Genesis 1:29-30',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen1-q16',
      questionType: 'multiple-choice',
      question: 'What was the condition of the earth before God began creating?',
      answers: [
        { id: 'a', text: 'Beautiful and organized', isCorrect: false },
        { id: 'b', text: 'Formless and empty', isCorrect: true },
        { id: 'c', text: 'Full of life', isCorrect: false },
        { id: 'd', text: 'Covered with plants', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'The earth was formless and empty, and darkness was over the surface of the deep.',
      bibleReference: 'Genesis 1:2',
      difficulty: 'medium',
      points: 1
    }
  ],
  totalQuestions: 16,
  estimatedTime: 8,
  difficulty: 'easy',
  testament: 'old',
  ageGroup: 'adult',
  tags: ['creation', 'genesis', 'beginning', 'god', 'bible'],
  isBookQuiz: false,
  isChapterQuiz: true,
  bookName: 'Genesis',
  chapterNumber: 1,
  createdAt: new Date('2025-01-01'),
  updatedAt: new Date('2025-01-01'),
  isPublished: true,
  completionCount: 0,
  averageScore: 0,
  seoTitle: 'Genesis Chapter 1 Quiz - Test Your Bible Knowledge | SalvationCall',
  seoDescription: 'Test your knowledge of Genesis chapter 1 with this interactive Bible quiz. 16 questions covering key verses, characters, and themes with instant results.',
  seoKeywords: ['genesis quiz', 'bible quiz', 'genesis chapter 1', 'creation quiz', 'scripture test', 'bible knowledge']
};

// Comprehensive Genesis Book Quiz - 50 questions covering the entire book
export const GENESIS_BOOK_QUIZ: Quiz = {
  id: 'genesis-book-quiz',
  title: 'Genesis Bible Quiz - Complete Book',
  slug: 'genesis-quiz',
  description: 'Test your comprehensive knowledge of Genesis with this 50-question Bible quiz covering creation, the patriarchs, and key events from the book of beginnings.',
  category: 'book',
  subcategory: 'old-testament',
  questions: [
    // Creation (Chapters 1-2)
    {
      id: 'gen-book-q1',
      questionType: 'multiple-choice',
      question: 'What did God create on the first day?',
      answers: [
        { id: 'a', text: 'The sun and moon', isCorrect: false },
        { id: 'b', text: 'Light and darkness', isCorrect: true },
        { id: 'c', text: 'The earth and sea', isCorrect: false },
        { id: 'd', text: 'Plants and trees', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'On the first day, God created light and separated it from darkness.',
      bibleReference: 'Genesis 1:3-5',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q2',
      questionType: 'multiple-choice',
      question: 'From what did God form man?',
      answers: [
        { id: 'a', text: 'Clay from the riverbank', isCorrect: false },
        { id: 'b', text: 'Dust from the ground', isCorrect: true },
        { id: 'c', text: 'Sand from the desert', isCorrect: false },
        { id: 'd', text: 'Stone from the mountain', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'The Lord God formed man from the dust of the ground and breathed into his nostrils the breath of life.',
      bibleReference: 'Genesis 2:7',
      difficulty: 'easy',
      points: 1
    },
    // The Fall (Chapter 3)
    {
      id: 'gen-book-q3',
      questionType: 'multiple-choice',
      question: 'What was the forbidden fruit from?',
      answers: [
        { id: 'a', text: 'The tree of life', isCorrect: false },
        { id: 'b', text: 'The tree of the knowledge of good and evil', isCorrect: true },
        { id: 'c', text: 'The tree of wisdom', isCorrect: false },
        { id: 'd', text: 'The tree of understanding', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God commanded them not to eat from the tree of the knowledge of good and evil.',
      bibleReference: 'Genesis 2:17, 3:6',
      difficulty: 'easy',
      points: 1
    },
    // Cain and Abel (Chapter 4)
    {
      id: 'gen-book-q4',
      questionType: 'multiple-choice',
      question: 'What was Abel\'s occupation?',
      answers: [
        { id: 'a', text: 'Farmer', isCorrect: false },
        { id: 'b', text: 'Shepherd', isCorrect: true },
        { id: 'c', text: 'Hunter', isCorrect: false },
        { id: 'd', text: 'Builder', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Abel was a keeper of sheep, while Cain was a tiller of the ground.',
      bibleReference: 'Genesis 4:2',
      difficulty: 'easy',
      points: 1
    },
    // Noah (Chapters 6-9)
    {
      id: 'gen-book-q5',
      questionType: 'multiple-choice',
      question: 'How many of each clean animal did Noah take into the ark?',
      answers: [
        { id: 'a', text: 'Two', isCorrect: false },
        { id: 'b', text: 'Seven pairs', isCorrect: true },
        { id: 'c', text: 'Ten', isCorrect: false },
        { id: 'd', text: 'Twelve', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Noah took seven pairs of every clean animal and one pair of every unclean animal.',
      bibleReference: 'Genesis 7:2-3',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q6',
      questionType: 'multiple-choice',
      question: 'What was the sign of God\'s covenant with Noah?',
      answers: [
        { id: 'a', text: 'A dove with an olive branch', isCorrect: false },
        { id: 'b', text: 'A rainbow', isCorrect: true },
        { id: 'c', text: 'A burning bush', isCorrect: false },
        { id: 'd', text: 'A pillar of cloud', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God set His rainbow in the cloud as a sign of the covenant that He would never again destroy the earth by flood.',
      bibleReference: 'Genesis 9:13',
      difficulty: 'easy',
      points: 1
    },
    // Tower of Babel (Chapter 11)
    {
      id: 'gen-book-q7',
      questionType: 'multiple-choice',
      question: 'Why did God confuse the languages at Babel?',
      answers: [
        { id: 'a', text: 'To punish their wickedness', isCorrect: false },
        { id: 'b', text: 'To scatter them across the earth', isCorrect: true },
        { id: 'c', text: 'To test their faith', isCorrect: false },
        { id: 'd', text: 'To teach them humility', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God confused their language so they would scatter across the earth rather than staying in one place.',
      bibleReference: 'Genesis 11:6-9',
      difficulty: 'medium',
      points: 1
    },
    // Abraham (Chapters 12-25)
    {
      id: 'gen-book-q8',
      questionType: 'multiple-choice',
      question: 'What was Abraham\'s name before God changed it?',
      answers: [
        { id: 'a', text: 'Abram', isCorrect: true },
        { id: 'b', text: 'Abner', isCorrect: false },
        { id: 'c', text: 'Abel', isCorrect: false },
        { id: 'd', text: 'Adam', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'God changed Abram\'s name to Abraham, meaning "father of many nations."',
      bibleReference: 'Genesis 17:5',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q9',
      questionType: 'multiple-choice',
      question: 'How old was Abraham when Isaac was born?',
      answers: [
        { id: 'a', text: '90 years old', isCorrect: false },
        { id: 'b', text: '100 years old', isCorrect: true },
        { id: 'c', text: '110 years old', isCorrect: false },
        { id: 'd', text: '120 years old', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Abraham was 100 years old when his son Isaac was born to him.',
      bibleReference: 'Genesis 21:5',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q10',
      questionType: 'multiple-choice',
      question: 'What did Abraham almost sacrifice on Mount Moriah?',
      answers: [
        { id: 'a', text: 'A ram', isCorrect: false },
        { id: 'b', text: 'His son Isaac', isCorrect: true },
        { id: 'c', text: 'A bull', isCorrect: false },
        { id: 'd', text: 'His servant', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God tested Abraham by asking him to sacrifice his son Isaac, but provided a ram instead.',
      bibleReference: 'Genesis 22:1-14',
      difficulty: 'easy',
      points: 1
    },
    // Isaac and Rebekah (Chapter 24)
    {
      id: 'gen-book-q11',
      questionType: 'multiple-choice',
      question: 'Who found a wife for Isaac?',
      answers: [
        { id: 'a', text: 'Abraham himself', isCorrect: false },
        { id: 'b', text: 'Abraham\'s servant', isCorrect: true },
        { id: 'c', text: 'Isaac found her himself', isCorrect: false },
        { id: 'd', text: 'Sarah before she died', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Abraham sent his servant to find a wife for Isaac from his own people.',
      bibleReference: 'Genesis 24:1-4',
      difficulty: 'medium',
      points: 1
    },
    // Jacob and Esau (Chapters 25-27)
    {
      id: 'gen-book-q12',
      questionType: 'multiple-choice',
      question: 'What did Esau sell to Jacob for a bowl of stew?',
      answers: [
        { id: 'a', text: 'His inheritance', isCorrect: false },
        { id: 'b', text: 'His birthright', isCorrect: true },
        { id: 'c', text: 'His blessing', isCorrect: false },
        { id: 'd', text: 'His land', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Esau sold his birthright to Jacob for a bowl of red stew when he was hungry.',
      bibleReference: 'Genesis 25:29-34',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q13',
      questionType: 'true-false',
      question: 'Jacob deceived his father Isaac to receive Esau\'s blessing.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'Jacob, with his mother Rebekah\'s help, disguised himself as Esau to receive Isaac\'s blessing.',
      bibleReference: 'Genesis 27:1-29',
      difficulty: 'easy',
      points: 1
    },
    // Jacob's Dream (Chapter 28)
    {
      id: 'gen-book-q14',
      questionType: 'multiple-choice',
      question: 'What did Jacob see in his dream at Bethel?',
      answers: [
        { id: 'a', text: 'A burning bush', isCorrect: false },
        { id: 'b', text: 'A ladder reaching to heaven', isCorrect: true },
        { id: 'c', text: 'A great flood', isCorrect: false },
        { id: 'd', text: 'A pillar of fire', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob dreamed of a ladder reaching to heaven with angels ascending and descending on it.',
      bibleReference: 'Genesis 28:10-15',
      difficulty: 'easy',
      points: 1
    },
    // Jacob and Laban (Chapters 29-31)
    {
      id: 'gen-book-q15',
      questionType: 'multiple-choice',
      question: 'How many years did Jacob work for Laban to marry Rachel?',
      answers: [
        { id: 'a', text: '7 years', isCorrect: false },
        { id: 'b', text: '14 years', isCorrect: true },
        { id: 'c', text: '20 years', isCorrect: false },
        { id: 'd', text: '21 years', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob worked 7 years for Rachel, but was given Leah first, then worked another 7 years for Rachel.',
      bibleReference: 'Genesis 29:18-30',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q16',
      questionType: 'multiple-choice',
      question: 'Who was Jacob\'s first wife?',
      answers: [
        { id: 'a', text: 'Rachel', isCorrect: false },
        { id: 'b', text: 'Leah', isCorrect: true },
        { id: 'c', text: 'Bilhah', isCorrect: false },
        { id: 'd', text: 'Zilpah', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Laban deceived Jacob and gave him Leah instead of Rachel on the wedding night.',
      bibleReference: 'Genesis 29:21-25',
      difficulty: 'medium',
      points: 1
    },
    // Jacob becomes Israel (Chapter 32)
    {
      id: 'gen-book-q17',
      questionType: 'multiple-choice',
      question: 'What new name did God give to Jacob?',
      answers: [
        { id: 'a', text: 'Abraham', isCorrect: false },
        { id: 'b', text: 'Israel', isCorrect: true },
        { id: 'c', text: 'Isaac', isCorrect: false },
        { id: 'd', text: 'Joseph', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God changed Jacob\'s name to Israel after he wrestled with the angel.',
      bibleReference: 'Genesis 32:28',
      difficulty: 'easy',
      points: 1
    },
    // Joseph's Dreams (Chapter 37)
    {
      id: 'gen-book-q18',
      questionType: 'multiple-choice',
      question: 'What did Joseph\'s brothers do to him out of jealousy?',
      answers: [
        { id: 'a', text: 'Killed him', isCorrect: false },
        { id: 'b', text: 'Sold him into slavery', isCorrect: true },
        { id: 'c', text: 'Banished him', isCorrect: false },
        { id: 'd', text: 'Imprisoned him', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Joseph\'s brothers sold him to Ishmaelite traders who took him to Egypt.',
      bibleReference: 'Genesis 37:25-28',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q19',
      questionType: 'multiple-choice',
      question: 'What special garment did Jacob give to Joseph?',
      answers: [
        { id: 'a', text: 'A coat of many colors', isCorrect: true },
        { id: 'b', text: 'A golden robe', isCorrect: false },
        { id: 'c', text: 'A priestly garment', isCorrect: false },
        { id: 'd', text: 'A royal crown', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'Jacob gave Joseph a coat of many colors because he loved him more than his other sons.',
      bibleReference: 'Genesis 37:3',
      difficulty: 'easy',
      points: 1
    },
    // Joseph in Egypt (Chapters 39-41)
    {
      id: 'gen-book-q20',
      questionType: 'multiple-choice',
      question: 'Who bought Joseph as a slave in Egypt?',
      answers: [
        { id: 'a', text: 'Pharaoh', isCorrect: false },
        { id: 'b', text: 'Potiphar', isCorrect: true },
        { id: 'c', text: 'The chief baker', isCorrect: false },
        { id: 'd', text: 'The chief butler', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Potiphar, an officer of Pharaoh and captain of the guard, bought Joseph.',
      bibleReference: 'Genesis 39:1',
      difficulty: 'medium',
      points: 1
    },
    // Pharaoh's Dreams (Chapter 41)
    {
      id: 'gen-book-q21',
      questionType: 'multiple-choice',
      question: 'How many years of famine did Joseph predict?',
      answers: [
        { id: 'a', text: 'Five years', isCorrect: false },
        { id: 'b', text: 'Seven years', isCorrect: true },
        { id: 'c', text: 'Ten years', isCorrect: false },
        { id: 'd', text: 'Twelve years', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Joseph interpreted Pharaoh\'s dream as seven years of plenty followed by seven years of famine.',
      bibleReference: 'Genesis 41:25-32',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q22',
      questionType: 'multiple-choice',
      question: 'What position did Pharaoh give to Joseph?',
      answers: [
        { id: 'a', text: 'Chief servant', isCorrect: false },
        { id: 'b', text: 'Second in command over Egypt', isCorrect: true },
        { id: 'c', text: 'Captain of the guard', isCorrect: false },
        { id: 'd', text: 'Chief priest', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Pharaoh made Joseph second in command over all Egypt to prepare for the famine.',
      bibleReference: 'Genesis 41:40-44',
      difficulty: 'medium',
      points: 1
    },
    // Joseph's Brothers Come to Egypt (Chapters 42-45)
    {
      id: 'gen-book-q23',
      questionType: 'multiple-choice',
      question: 'Which brother did Joseph keep as a hostage when his brothers first came to Egypt?',
      answers: [
        { id: 'a', text: 'Reuben', isCorrect: false },
        { id: 'b', text: 'Simeon', isCorrect: true },
        { id: 'c', text: 'Judah', isCorrect: false },
        { id: 'd', text: 'Benjamin', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Joseph kept Simeon bound as a hostage until his brothers returned with Benjamin.',
      bibleReference: 'Genesis 42:24',
      difficulty: 'hard',
      points: 1
    },
    {
      id: 'gen-book-q24',
      questionType: 'true-false',
      question: 'Joseph immediately revealed his identity to his brothers when they came to Egypt.',
      answers: [
        { id: 'true', text: 'True', isCorrect: false },
        { id: 'false', text: 'False', isCorrect: true }
      ],
      correctAnswerId: 'false',
      explanation: 'Joseph tested his brothers multiple times before revealing his identity to them.',
      bibleReference: 'Genesis 42-45',
      difficulty: 'medium',
      points: 1
    },
    // Jacob's Family Moves to Egypt (Chapters 46-47)
    {
      id: 'gen-book-q25',
      questionType: 'multiple-choice',
      question: 'How many people from Jacob\'s family went down to Egypt?',
      answers: [
        { id: 'a', text: '66', isCorrect: false },
        { id: 'b', text: '70', isCorrect: true },
        { id: 'c', text: '75', isCorrect: false },
        { id: 'd', text: '80', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'All the souls of the house of Jacob who came to Egypt were seventy.',
      bibleReference: 'Genesis 46:27',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q26',
      questionType: 'multiple-choice',
      question: 'In what region of Egypt did Jacob\'s family settle?',
      answers: [
        { id: 'a', text: 'Memphis', isCorrect: false },
        { id: 'b', text: 'Goshen', isCorrect: true },
        { id: 'c', text: 'Thebes', isCorrect: false },
        { id: 'd', text: 'Alexandria', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Joseph settled his family in the land of Goshen, the best part of Egypt.',
      bibleReference: 'Genesis 47:11',
      difficulty: 'medium',
      points: 1
    },
    // Jacob's Blessings (Chapters 48-49)
    {
      id: 'gen-book-q27',
      questionType: 'multiple-choice',
      question: 'Which of Joseph\'s sons received the greater blessing from Jacob?',
      answers: [
        { id: 'a', text: 'Manasseh (the older)', isCorrect: false },
        { id: 'b', text: 'Ephraim (the younger)', isCorrect: true },
        { id: 'c', text: 'Both received equal blessings', isCorrect: false },
        { id: 'd', text: 'Neither was blessed', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob crossed his hands and gave the greater blessing to Ephraim, the younger son.',
      bibleReference: 'Genesis 48:13-20',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q28',
      questionType: 'multiple-choice',
      question: 'Jacob compared Judah to which animal in his blessing?',
      answers: [
        { id: 'a', text: 'A wolf', isCorrect: false },
        { id: 'b', text: 'A lion', isCorrect: true },
        { id: 'c', text: 'A serpent', isCorrect: false },
        { id: 'd', text: 'A deer', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob said "Judah is a lion\'s whelp" and prophesied that the scepter would not depart from Judah.',
      bibleReference: 'Genesis 49:9-10',
      difficulty: 'medium',
      points: 1
    },
    // Deaths and Endings (Chapter 50)
    {
      id: 'gen-book-q29',
      questionType: 'multiple-choice',
      question: 'How old was Jacob when he died?',
      answers: [
        { id: 'a', text: '120 years', isCorrect: false },
        { id: 'b', text: '147 years', isCorrect: true },
        { id: 'c', text: '175 years', isCorrect: false },
        { id: 'd', text: '180 years', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob lived 147 years before he died in Egypt.',
      bibleReference: 'Genesis 47:28',
      difficulty: 'hard',
      points: 1
    },
    {
      id: 'gen-book-q30',
      questionType: 'multiple-choice',
      question: 'Where was Jacob buried?',
      answers: [
        { id: 'a', text: 'In Egypt', isCorrect: false },
        { id: 'b', text: 'In the cave of Machpelah', isCorrect: true },
        { id: 'c', text: 'In Bethel', isCorrect: false },
        { id: 'd', text: 'In Beersheba', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob was buried in the cave of Machpelah with Abraham, Sarah, Isaac, Rebekah, and Leah.',
      bibleReference: 'Genesis 50:13',
      difficulty: 'medium',
      points: 1
    },
    // Additional Comprehensive Questions
    {
      id: 'gen-book-q31',
      questionType: 'true-false',
      question: 'God created man and woman on the same day.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'God created both male and female on the sixth day of creation.',
      bibleReference: 'Genesis 1:27',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q32',
      questionType: 'multiple-choice',
      question: 'What was the name of the garden where Adam and Eve lived?',
      answers: [
        { id: 'a', text: 'Garden of Paradise', isCorrect: false },
        { id: 'b', text: 'Garden of Eden', isCorrect: true },
        { id: 'c', text: 'Garden of Peace', isCorrect: false },
        { id: 'd', text: 'Garden of Life', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God planted a garden eastward in Eden and put the man there.',
      bibleReference: 'Genesis 2:8',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q33',
      questionType: 'multiple-choice',
      question: 'What was the first thing Noah did after leaving the ark?',
      answers: [
        { id: 'a', text: 'Built a house', isCorrect: false },
        { id: 'b', text: 'Built an altar and offered sacrifices', isCorrect: true },
        { id: 'c', text: 'Planted a vineyard', isCorrect: false },
        { id: 'd', text: 'Gathered his family', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Noah built an altar to the Lord and offered burnt offerings.',
      bibleReference: 'Genesis 8:20',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q34',
      questionType: 'multiple-choice',
      question: 'How many sons did Noah have?',
      answers: [
        { id: 'a', text: 'Two', isCorrect: false },
        { id: 'b', text: 'Three', isCorrect: true },
        { id: 'c', text: 'Four', isCorrect: false },
        { id: 'd', text: 'Five', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Noah had three sons: Shem, Ham, and Japheth.',
      bibleReference: 'Genesis 6:10',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q35',
      questionType: 'multiple-choice',
      question: 'What did God promise Abraham regarding his descendants?',
      answers: [
        { id: 'a', text: 'They would be as numerous as the stars', isCorrect: true },
        { id: 'b', text: 'They would be mighty warriors', isCorrect: false },
        { id: 'c', text: 'They would be very wealthy', isCorrect: false },
        { id: 'd', text: 'They would live forever', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'God promised Abraham that his descendants would be as numerous as the stars in heaven.',
      bibleReference: 'Genesis 15:5',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q36',
      questionType: 'multiple-choice',
      question: 'What was the name of Abraham\'s nephew?',
      answers: [
        { id: 'a', text: 'Isaac', isCorrect: false },
        { id: 'b', text: 'Lot', isCorrect: true },
        { id: 'c', text: 'Laban', isCorrect: false },
        { id: 'd', text: 'Nahor', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Lot was Abraham\'s nephew who traveled with him and later settled in Sodom.',
      bibleReference: 'Genesis 12:5',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q37',
      questionType: 'true-false',
      question: 'Sarah laughed when she heard she would have a son in her old age.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'Sarah laughed within herself when she heard the Lord say she would bear a son.',
      bibleReference: 'Genesis 18:12',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q38',
      questionType: 'multiple-choice',
      question: 'What cities were destroyed along with Sodom?',
      answers: [
        { id: 'a', text: 'Gomorrah', isCorrect: true },
        { id: 'b', text: 'Bethel', isCorrect: false },
        { id: 'c', text: 'Hebron', isCorrect: false },
        { id: 'd', text: 'Beersheba', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'God destroyed both Sodom and Gomorrah with fire and brimstone.',
      bibleReference: 'Genesis 19:24',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q39',
      questionType: 'multiple-choice',
      question: 'How many sons did Jacob have?',
      answers: [
        { id: 'a', text: '10', isCorrect: false },
        { id: 'b', text: '12', isCorrect: true },
        { id: 'c', text: '14', isCorrect: false },
        { id: 'd', text: '16', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob had twelve sons who became the fathers of the twelve tribes of Israel.',
      bibleReference: 'Genesis 35:22',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q40',
      questionType: 'multiple-choice',
      question: 'What did Joseph\'s brothers tell their father happened to Joseph?',
      answers: [
        { id: 'a', text: 'He ran away', isCorrect: false },
        { id: 'b', text: 'He was killed by a wild animal', isCorrect: true },
        { id: 'c', text: 'He was kidnapped', isCorrect: false },
        { id: 'd', text: 'He fell into a pit', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'The brothers showed Jacob Joseph\'s bloodied coat and let him believe a wild animal killed Joseph.',
      bibleReference: 'Genesis 37:31-33',
      difficulty: 'medium',
      points: 1
    },
    // Final 10 unique questions to complete the 50-question Genesis quiz
    {
      id: 'gen-book-q41',
      questionType: 'multiple-choice',
      question: 'What was the sign of God\'s covenant with Noah?',
      answers: [
        { id: 'a', text: 'A dove with an olive branch', isCorrect: false },
        { id: 'b', text: 'A rainbow in the clouds', isCorrect: true },
        { id: 'c', text: 'A pillar of fire', isCorrect: false },
        { id: 'd', text: 'A burning bush', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God set His rainbow in the cloud as a sign of the covenant between Him and the earth.',
      bibleReference: 'Genesis 9:13',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q42',
      questionType: 'true-false',
      question: 'Abraham was originally called Abram.',
      answers: [
        { id: 'true', text: 'True', isCorrect: true },
        { id: 'false', text: 'False', isCorrect: false }
      ],
      correctAnswerId: 'true',
      explanation: 'God changed Abram\'s name to Abraham when He made the covenant with him.',
      bibleReference: 'Genesis 17:5',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q43',
      questionType: 'multiple-choice',
      question: 'What did God promise to make of Ishmael?',
      answers: [
        { id: 'a', text: 'A priest', isCorrect: false },
        { id: 'b', text: 'A great nation', isCorrect: true },
        { id: 'c', text: 'A king', isCorrect: false },
        { id: 'd', text: 'A prophet', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'God promised Abraham that He would make Ishmael into a great nation.',
      bibleReference: 'Genesis 17:20',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q44',
      questionType: 'multiple-choice',
      question: 'What did Rebekah do when she first saw Isaac?',
      answers: [
        { id: 'a', text: 'She ran to meet him', isCorrect: false },
        { id: 'b', text: 'She covered herself with a veil', isCorrect: true },
        { id: 'c', text: 'She hid behind the camels', isCorrect: false },
        { id: 'd', text: 'She called out to him', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'When Rebekah saw Isaac, she took a veil and covered herself.',
      bibleReference: 'Genesis 24:65',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q45',
      questionType: 'multiple-choice',
      question: 'What did Jacob use as a pillow when he had his dream?',
      answers: [
        { id: 'a', text: 'A bundle of clothes', isCorrect: false },
        { id: 'b', text: 'A stone', isCorrect: true },
        { id: 'c', text: 'A bag of grain', isCorrect: false },
        { id: 'd', text: 'A wooden block', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob took one of the stones of the place and put it under his head for a pillow.',
      bibleReference: 'Genesis 28:11',
      difficulty: 'easy',
      points: 1
    },
    {
      id: 'gen-book-q46',
      questionType: 'multiple-choice',
      question: 'How many years did Jacob serve Laban in total?',
      answers: [
        { id: 'a', text: '14 years', isCorrect: false },
        { id: 'b', text: '20 years', isCorrect: true },
        { id: 'c', text: '21 years', isCorrect: false },
        { id: 'd', text: '25 years', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Jacob served Laban for 20 years total - 14 for his wives and 6 for his cattle.',
      bibleReference: 'Genesis 31:41',
      difficulty: 'hard',
      points: 1
    },
    {
      id: 'gen-book-q47',
      questionType: 'true-false',
      question: 'Joseph was the youngest of Jacob\'s sons.',
      answers: [
        { id: 'true', text: 'True', isCorrect: false },
        { id: 'false', text: 'False', isCorrect: true }
      ],
      correctAnswerId: 'false',
      explanation: 'Benjamin was the youngest son. Joseph was the second youngest.',
      bibleReference: 'Genesis 35:18',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q48',
      questionType: 'multiple-choice',
      question: 'What did Pharaoh give Joseph as symbols of his authority?',
      answers: [
        { id: 'a', text: 'A crown and scepter', isCorrect: false },
        { id: 'b', text: 'A signet ring and gold chain', isCorrect: true },
        { id: 'c', text: 'A sword and shield', isCorrect: false },
        { id: 'd', text: 'A staff and robe', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Pharaoh gave Joseph his signet ring and put a gold chain around his neck.',
      bibleReference: 'Genesis 41:42',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q49',
      questionType: 'multiple-choice',
      question: 'What did Joseph\'s brothers bring to Egypt to trade for grain?',
      answers: [
        { id: 'a', text: 'Silver', isCorrect: true },
        { id: 'b', text: 'Gold', isCorrect: false },
        { id: 'c', text: 'Livestock', isCorrect: false },
        { id: 'd', text: 'Spices', isCorrect: false }
      ],
      correctAnswerId: 'a',
      explanation: 'Jacob told his sons to take silver in their hands to buy grain in Egypt.',
      bibleReference: 'Genesis 43:12',
      difficulty: 'medium',
      points: 1
    },
    {
      id: 'gen-book-q50',
      questionType: 'multiple-choice',
      question: 'What was the name of the land where Jacob\'s family settled in Egypt?',
      answers: [
        { id: 'a', text: 'Memphis', isCorrect: false },
        { id: 'b', text: 'Goshen', isCorrect: true },
        { id: 'c', text: 'Thebes', isCorrect: false },
        { id: 'd', text: 'Alexandria', isCorrect: false }
      ],
      correctAnswerId: 'b',
      explanation: 'Joseph gave his father and brothers a possession in the land of Egypt, in the best of the land, in the land of Rameses (Goshen).',
      bibleReference: 'Genesis 47:11',
      difficulty: 'medium',
      points: 1
    }
  ],
  totalQuestions: 50,
  estimatedTime: 30,
  difficulty: 'medium',
  testament: 'old',
  ageGroup: 'adult',
  tags: ['genesis', 'creation', 'abraham', 'isaac', 'jacob', 'joseph', 'noah', 'old-testament', 'patriarchs'],
  isBookQuiz: true,
  isChapterQuiz: false,
  bookName: 'Genesis',
  createdAt: new Date('2025-01-01'),
  updatedAt: new Date('2025-01-01'),
  isPublished: true,
  completionCount: 0,
  averageScore: 0,
  seoTitle: 'Genesis Bible Quiz - Complete Book Test | SalvationCall',
  seoDescription: 'Test your comprehensive knowledge of Genesis with this 50-question Bible quiz covering creation, Abraham, Isaac, Jacob, Joseph, and all major events in the book of beginnings.',
  seoKeywords: ['genesis quiz', 'genesis bible quiz', 'complete genesis quiz', 'bible quiz', 'old testament quiz', 'creation quiz', 'abraham quiz', 'patriarchs quiz']
};

// Sample character quiz for Abraham
export const ABRAHAM_QUIZ: Quiz = {
  id: 'abraham-quiz',
  title: 'Abraham Bible Quiz',
  slug: 'abraham-quiz',
  description: 'Test your knowledge about Abraham, the father of faith, with this comprehensive Bible quiz covering his life, faith journey, and key biblical moments.',
  category: 'character',
  subcategory: 'old-testament',
  questions: [], // Will be populated with Abraham-specific questions
  totalQuestions: 20,
  estimatedTime: 12,
  difficulty: 'medium',
  testament: 'old',
  ageGroup: 'adult',
  tags: ['abraham', 'faith', 'covenant', 'patriarch', 'old-testament'],
  isBookQuiz: false,
  isChapterQuiz: false,
  characterName: 'Abraham',
  createdAt: new Date('2025-01-01'),
  updatedAt: new Date('2025-01-01'),
  isPublished: true,
  completionCount: 0,
  averageScore: 0,
  seoTitle: 'Abraham Bible Quiz - Test Your Knowledge | SalvationCall',
  seoDescription: 'Challenge yourself with this Abraham Bible quiz! 20 questions about his life, faith journey, and key biblical moments. Test your knowledge now!',
  seoKeywords: ['abraham quiz', 'bible quiz', 'abraham bible', 'faith quiz', 'patriarch quiz', 'old testament characters']
};

export const SAMPLE_QUIZZES = [GENESIS_1_QUIZ, GENESIS_BOOK_QUIZ, ABRAHAM_QUIZ];
