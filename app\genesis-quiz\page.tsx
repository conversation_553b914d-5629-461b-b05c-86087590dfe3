import type { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { GENESIS_BOOK_QUIZ } from '../../lib/data/sample-quizzes';

// Dynamically import QuizContainer to prevent hydration issues
const QuizContainer = dynamic(() => import('../../components/quiz/QuizContainer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading quiz...</p>
      </div>
    </div>
  )
});

export const metadata: Metadata = {
  title: 'Genesis Bible Quiz - Complete Book Test | SalvationCall',
  description: 'Test your comprehensive knowledge of Genesis with this 50-question Bible quiz covering creation, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and all major events in the book of beginnings.',
  keywords: ['genesis quiz', 'genesis bible quiz', 'complete genesis quiz', 'bible quiz', 'old testament quiz', 'creation quiz', 'abraham quiz', 'patriarchs quiz'],
};

export default function GenesisQuizPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Genesis Bible Quiz</h1>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900">Questions</h3>
                <p className="text-2xl font-bold text-blue-600">{GENESIS_BOOK_QUIZ.questions.length}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-900">Difficulty</h3>
                <p className="text-lg font-semibold text-green-600 capitalize">{GENESIS_BOOK_QUIZ.difficulty}</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-semibold text-purple-900">Time</h3>
                <p className="text-lg font-semibold text-purple-600">{GENESIS_BOOK_QUIZ.estimatedTime} min</p>
              </div>
            </div>
            <p className="text-gray-600 mb-6">{GENESIS_BOOK_QUIZ.description}</p>
          </div>

          <QuizContainer quiz={GENESIS_BOOK_QUIZ} />
        </div>
      </div>
    </div>
  );
}
