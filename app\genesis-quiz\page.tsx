import type { <PERSON>ada<PERSON> } from 'next';
import QuizContainer from '../../components/quiz/QuizContainer';
import { GENESIS_BOOK_QUIZ } from '../../lib/data/sample-quizzes';

export const metadata: Metadata = {
  title: 'Genesis Bible Quiz - Complete Book Test | SalvationCall',
  description: 'Test your comprehensive knowledge of Genesis with this 50-question Bible quiz covering creation, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and all major events in the book of beginnings.',
  keywords: ['genesis quiz', 'genesis bible quiz', 'complete genesis quiz', 'bible quiz', 'old testament quiz', 'creation quiz', 'abraham quiz', 'patriarchs quiz'],
};

export default function GenesisQuizPage() {
  return <QuizContainer quiz={GENESIS_BOOK_QUIZ} />;
}
