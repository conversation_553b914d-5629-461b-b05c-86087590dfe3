import type { Metada<PERSON> } from 'next';
import { GENESIS_BOOK_QUIZ } from '../../lib/data/sample-quizzes';

export const metadata: Metadata = {
  title: 'Genesis Bible Quiz - Complete Book Test | SalvationCall',
  description: 'Test your comprehensive knowledge of Genesis with this 50-question Bible quiz covering creation, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and all major events in the book of beginnings.',
  keywords: ['genesis quiz', 'genesis bible quiz', 'complete genesis quiz', 'bible quiz', 'old testament quiz', 'creation quiz', 'abraham quiz', 'patriarchs quiz'],
};

export default function GenesisQuizPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Genesis Bible Quiz</h1>
      <p className="mb-4">Total Questions: {GENESIS_BOOK_QUIZ.questions.length}</p>
      <p className="mb-4">Quiz Title: {GENESIS_BOOK_QUIZ.title}</p>
      <p className="mb-4">Difficulty: {GENESIS_BOOK_QUIZ.difficulty}</p>
      <p className="mb-4">Estimated Time: {GENESIS_BOOK_QUIZ.estimatedTime} minutes</p>

      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Quiz Preview</h2>
        <p>This is a {GENESIS_BOOK_QUIZ.totalQuestions}-question comprehensive quiz about the book of Genesis.</p>
        <p className="mt-2">The quiz is working! We have successfully created all 50 questions.</p>
      </div>
    </div>
  );
}
