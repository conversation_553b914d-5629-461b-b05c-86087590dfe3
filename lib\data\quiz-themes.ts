import { QuizTheme, DifficultyLevel } from '../types/quiz';

export const QUIZ_THEMES: QuizTheme[] = [
  {
    id: 'miracles-of-jesus',
    name: 'Miracles of Jesus',
    slug: 'miracles-of-jesus',
    description: 'The supernatural works of <PERSON> that demonstrated His divine power and compassion.',
    relatedBooks: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    keyVerses: ['John 2:11', 'John 20:30-31', 'Matthew 11:4-5'],
    difficulty: 'medium'
  },
  {
    id: 'parables',
    name: 'Parables of <PERSON>',
    slug: 'parables',
    description: 'The earthly stories with heavenly meanings that <PERSON> used to teach spiritual truths.',
    relatedBooks: ['<PERSON>', '<PERSON>', '<PERSON>'],
    keyVerses: ['Matthew 13:3', 'Mark 4:2', 'Luke 8:4'],
    difficulty: 'medium'
  },
  {
    id: 'ten-commandments',
    name: 'Ten Commandments',
    slug: 'ten-commandments',
    description: 'The fundamental moral laws given by <PERSON> to <PERSON> on Mount Sinai.',
    relatedBooks: ['Exodus', 'Deuteronomy'],
    keyVerses: ['Exodus 20:1-17', 'Deuteronomy 5:4-21'],
    difficulty: 'easy'
  },
  {
    id: 'fruits-of-spirit',
    name: 'Fruits of the Spirit',
    slug: 'fruits-of-spirit',
    description: 'The character qualities that the Holy Spirit produces in believers.',
    relatedBooks: ['Galatians', 'Ephesians', 'Colossians'],
    keyVerses: ['Galatians 5:22-23', 'Ephesians 5:9', 'Colossians 3:12-14'],
    difficulty: 'medium'
  },
  {
    id: 'armor-of-god',
    name: 'Armor of God',
    slug: 'armor-of-god',
    description: 'The spiritual protection and weapons God provides for spiritual warfare.',
    relatedBooks: ['Ephesians'],
    keyVerses: ['Ephesians 6:10-18'],
    difficulty: 'medium'
  },
  {
    id: 'biblical-prophecy',
    name: 'Biblical Prophecy',
    slug: 'biblical-prophecy',
    description: 'God\'s revelation of future events and His plans for humanity.',
    relatedBooks: ['Isaiah', 'Jeremiah', 'Ezekiel', 'Daniel', 'Revelation'],
    keyVerses: ['2 Peter 1:20-21', 'Revelation 1:3', 'Daniel 2:28'],
    difficulty: 'hard'
  },
  {
    id: 'prayer-in-bible',
    name: 'Prayer in the Bible',
    slug: 'prayer-in-bible',
    description: 'Biblical teachings and examples of communication with God through prayer.',
    relatedBooks: ['Matthew', 'Luke', '1 Thessalonians', 'James'],
    keyVerses: ['Matthew 6:9-13', 'Luke 11:1-4', '1 Thessalonians 5:17', 'James 5:16'],
    difficulty: 'medium'
  },
  {
    id: 'love-in-scripture',
    name: 'Love in Scripture',
    slug: 'love-in-scripture',
    description: 'God\'s love for humanity and the call for believers to love one another.',
    relatedBooks: ['1 John', '1 Corinthians', 'John', 'Romans'],
    keyVerses: ['1 John 4:7-21', '1 Corinthians 13:1-13', 'John 3:16', 'Romans 5:8'],
    difficulty: 'easy'
  },
  {
    id: 'creation',
    name: 'Creation',
    slug: 'creation',
    description: 'God\'s creation of the universe, earth, and all living things.',
    relatedBooks: ['Genesis', 'Psalms', 'Job', 'Colossians'],
    keyVerses: ['Genesis 1:1', 'Psalm 19:1', 'Job 38:4', 'Colossians 1:16'],
    difficulty: 'easy'
  },
  {
    id: 'salvation',
    name: 'Salvation',
    slug: 'salvation',
    description: 'God\'s plan of redemption through Jesus Christ for eternal life.',
    relatedBooks: ['Romans', 'Ephesians', 'John', 'Acts'],
    keyVerses: ['Romans 3:23', 'Romans 6:23', 'Ephesians 2:8-9', 'John 3:16', 'Acts 4:12'],
    difficulty: 'medium'
  },
  {
    id: 'faith',
    name: 'Faith',
    slug: 'faith',
    description: 'Trust and belief in God and His promises throughout Scripture.',
    relatedBooks: ['Hebrews', 'Romans', 'James', 'Habakkuk'],
    keyVerses: ['Hebrews 11:1', 'Romans 1:17', 'James 2:17', 'Habakkuk 2:4'],
    difficulty: 'medium'
  },
  {
    id: 'wisdom',
    name: 'Biblical Wisdom',
    slug: 'wisdom',
    description: 'God\'s wisdom for living and making righteous decisions.',
    relatedBooks: ['Proverbs', 'Ecclesiastes', 'James', '1 Corinthians'],
    keyVerses: ['Proverbs 9:10', 'James 1:5', '1 Corinthians 1:25'],
    difficulty: 'medium'
  },
  {
    id: 'forgiveness',
    name: 'Forgiveness',
    slug: 'forgiveness',
    description: 'God\'s forgiveness of sin and the call to forgive others.',
    relatedBooks: ['Matthew', 'Luke', 'Ephesians', '1 John'],
    keyVerses: ['Matthew 6:14-15', 'Luke 23:34', 'Ephesians 4:32', '1 John 1:9'],
    difficulty: 'medium'
  },
  {
    id: 'hope',
    name: 'Hope',
    slug: 'hope',
    description: 'The confident expectation of God\'s promises and eternal life.',
    relatedBooks: ['Romans', '1 Peter', 'Hebrews', 'Revelation'],
    keyVerses: ['Romans 15:13', '1 Peter 1:3', 'Hebrews 6:19'],
    difficulty: 'medium'
  },
  {
    id: 'peace',
    name: 'Peace',
    slug: 'peace',
    description: 'The peace that comes from God and reconciliation through Christ.',
    relatedBooks: ['John', 'Philippians', 'Isaiah', 'Romans'],
    keyVerses: ['John 14:27', 'Philippians 4:7', 'Isaiah 26:3', 'Romans 5:1'],
    difficulty: 'medium'
  },
  {
    id: 'joy',
    name: 'Joy',
    slug: 'joy',
    description: 'The deep gladness that comes from knowing and serving God.',
    relatedBooks: ['Philippians', 'Psalms', 'Nehemiah', 'John'],
    keyVerses: ['Philippians 4:4', 'Psalm 16:11', 'Nehemiah 8:10', 'John 15:11'],
    difficulty: 'easy'
  },
  {
    id: 'angels',
    name: 'Angels',
    slug: 'angels',
    description: 'God\'s heavenly messengers and their role in biblical history.',
    relatedBooks: ['Genesis', 'Daniel', 'Luke', 'Hebrews', 'Revelation'],
    keyVerses: ['Hebrews 1:14', 'Luke 1:26-38', 'Daniel 10:12'],
    difficulty: 'medium'
  },
  {
    id: 'covenant',
    name: 'God\'s Covenants',
    slug: 'covenant',
    description: 'The promises and agreements God made with His people.',
    relatedBooks: ['Genesis', 'Exodus', 'Jeremiah', 'Hebrews'],
    keyVerses: ['Genesis 9:13', 'Exodus 24:8', 'Jeremiah 31:31', 'Hebrews 8:6'],
    difficulty: 'hard'
  },
  {
    id: 'worship',
    name: 'Worship',
    slug: 'worship',
    description: 'The proper way to honor, praise, and serve God.',
    relatedBooks: ['Psalms', 'John', 'Romans', 'Revelation'],
    keyVerses: ['John 4:24', 'Romans 12:1', 'Psalm 95:6', 'Revelation 4:11'],
    difficulty: 'medium'
  },
  {
    id: 'discipleship',
    name: 'Discipleship',
    slug: 'discipleship',
    description: 'Following Jesus and growing in Christian maturity.',
    relatedBooks: ['Matthew', 'Luke', 'John', '2 Timothy'],
    keyVerses: ['Matthew 28:19-20', 'Luke 9:23', 'John 8:31', '2 Timothy 2:2'],
    difficulty: 'medium'
  }
];

export function getQuizThemeBySlug(slug: string): QuizTheme | undefined {
  return QUIZ_THEMES.find(theme => theme.slug === slug);
}

export function getQuizThemeById(id: string): QuizTheme | undefined {
  return QUIZ_THEMES.find(theme => theme.id === id);
}

export function getThemesByDifficulty(difficulty: DifficultyLevel): QuizTheme[] {
  return QUIZ_THEMES.filter(theme => theme.difficulty === difficulty);
}
