import type { <PERSON>ada<PERSON> } from 'next';
import QuizContainer from '../../components/quiz/QuizContainer';
import { GENESIS_1_QUIZ } from '../../lib/data/sample-quizzes';

export const metadata: Metadata = {
  title: 'Genesis Chapter 1 Quiz - Test Your Bible Knowledge | SalvationCall',
  description: 'Test your knowledge of Genesis chapter 1 with this interactive Bible quiz. 16 questions covering key verses, characters, and themes with instant results.',
  keywords: ['genesis quiz', 'bible quiz', 'genesis chapter 1', 'creation quiz', 'scripture test', 'bible knowledge'],
};

export default function Genesis1QuizPage() {
  return <QuizContainer quiz={GENESIS_1_QUIZ} />;
}
