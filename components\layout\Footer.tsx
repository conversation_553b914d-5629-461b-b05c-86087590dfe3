import Link from 'next/link';
import { BIBLE_BOOKS } from '../../lib/data/bible-books';

const Footer = () => {
  const oldTestamentBooks = BIBLE_BOOKS.filter(book => book.testament === 'old').slice(0, 20); // First 20 for space
  const newTestamentBooks = BIBLE_BOOKS.filter(book => book.testament === 'new').slice(0, 15); // First 15 for space

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Column 1: Bible Quizzes */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Bible Quizzes</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/genesis-quiz" className="text-gray-300 hover:text-white transition-colors">Genesis Quiz</Link></li>
              <li><Link href="/matthew-quiz" className="text-gray-300 hover:text-white transition-colors">Matthew Quiz</Link></li>
              <li><Link href="/john-quiz" className="text-gray-300 hover:text-white transition-colors">John Quiz</Link></li>
              <li><Link href="/romans-quiz" className="text-gray-300 hover:text-white transition-colors">Romans Quiz</Link></li>
              <li><Link href="/psalms-quiz" className="text-gray-300 hover:text-white transition-colors">Psalms Quiz</Link></li>
              <li><Link href="/revelation-quiz" className="text-gray-300 hover:text-white transition-colors">Revelation Quiz</Link></li>
              <li><Link href="/jesus-quiz" className="text-gray-300 hover:text-white transition-colors">Jesus Quiz</Link></li>
              <li><Link href="/moses-quiz" className="text-gray-300 hover:text-white transition-colors">Moses Quiz</Link></li>
              <li><Link href="/david-quiz" className="text-gray-300 hover:text-white transition-colors">David Quiz</Link></li>
              <li><Link href="/paul-quiz" className="text-gray-300 hover:text-white transition-colors">Paul Quiz</Link></li>
              <li><Link href="/bible-quizzes" className="text-blue-400 hover:text-blue-300 transition-colors font-medium">All Quizzes →</Link></li>
            </ul>
          </div>

          {/* Column 2: Study Resources */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Study Resources</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/bible-study-plans" className="text-gray-300 hover:text-white transition-colors">Bible Study Plans</Link></li>
              <li><Link href="/bible-chapter-summaries" className="text-gray-300 hover:text-white transition-colors">Chapter Summaries</Link></li>
              <li><Link href="/bible-book-overviews" className="text-gray-300 hover:text-white transition-colors">Book Overviews</Link></li>
              <li><Link href="/bible-character-studies" className="text-gray-300 hover:text-white transition-colors">Character Studies</Link></li>
              <li><Link href="/bible-verses-explained" className="text-gray-300 hover:text-white transition-colors">Verse Explanations</Link></li>
              <li><Link href="/bible-memory-verses" className="text-gray-300 hover:text-white transition-colors">Memory Verses</Link></li>
              <li><Link href="/bible-discussion-questions" className="text-gray-300 hover:text-white transition-colors">Discussion Questions</Link></li>
              <li><Link href="/bible-timeline" className="text-gray-300 hover:text-white transition-colors">Bible Timeline</Link></li>
              <li><Link href="/bible-maps-charts" className="text-gray-300 hover:text-white transition-colors">Maps & Charts</Link></li>
              <li><Link href="/printable-bible-resources" className="text-gray-300 hover:text-white transition-colors">Printable Resources</Link></li>
              <li><Link href="/bible-study-guides" className="text-blue-400 hover:text-blue-300 transition-colors font-medium">All Resources →</Link></li>
            </ul>
          </div>

          {/* Column 3: Popular Topics */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Popular Topics</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/ten-commandments-quiz" className="text-gray-300 hover:text-white transition-colors">Ten Commandments</Link></li>
              <li><Link href="/lords-prayer-quiz" className="text-gray-300 hover:text-white transition-colors">Lord's Prayer</Link></li>
              <li><Link href="/miracles-of-jesus-quiz" className="text-gray-300 hover:text-white transition-colors">Miracles of Jesus</Link></li>
              <li><Link href="/parables-quiz" className="text-gray-300 hover:text-white transition-colors">Parables</Link></li>
              <li><Link href="/christmas-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Christmas</Link></li>
              <li><Link href="/easter-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Easter</Link></li>
              <li><Link href="/creation-quiz" className="text-gray-300 hover:text-white transition-colors">Creation</Link></li>
              <li><Link href="/noahs-ark-quiz" className="text-gray-300 hover:text-white transition-colors">Noah's Ark</Link></li>
              <li><Link href="/exodus-quiz" className="text-gray-300 hover:text-white transition-colors">Exodus</Link></li>
              <li><Link href="/crucifixion-quiz" className="text-gray-300 hover:text-white transition-colors">Crucifixion</Link></li>
              <li><Link href="/bible-quiz-topics" className="text-blue-400 hover:text-blue-300 transition-colors font-medium">All Topics →</Link></li>
            </ul>
          </div>

          {/* Column 4: Site Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Site Information</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/about" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
              <li><Link href="/contact" className="text-gray-300 hover:text-white transition-colors">Contact</Link></li>
              <li><Link href="/how-bible-quiz-works" className="text-gray-300 hover:text-white transition-colors">How It Works</Link></li>
              <li><Link href="/bible-quiz-faq" className="text-gray-300 hover:text-white transition-colors">FAQ</Link></li>
              <li><Link href="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link href="/terms-of-service" className="text-gray-300 hover:text-white transition-colors">Terms of Service</Link></li>
              <li><Link href="/sitemap" className="text-gray-300 hover:text-white transition-colors">Sitemap</Link></li>
              <li><Link href="/newsletter-signup" className="text-gray-300 hover:text-white transition-colors">Newsletter</Link></li>
              <li><Link href="/submit-quiz-questions" className="text-gray-300 hover:text-white transition-colors">Submit Quiz</Link></li>
              <li><Link href="/feedback" className="text-gray-300 hover:text-white transition-colors">Feedback</Link></li>
            </ul>
          </div>
        </div>

        {/* Bible Books Section */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <h3 className="text-lg font-semibold mb-6 text-center">Bible Books</h3>
          
          {/* Old Testament */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-400 mb-3">Old Testament</h4>
            <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 text-xs">
              {oldTestamentBooks.map((book) => (
                <Link 
                  key={book.id} 
                  href={`/${book.slug}-quiz`}
                  className="text-gray-300 hover:text-white transition-colors py-1"
                >
                  {book.name}
                </Link>
              ))}
              <Link href="/old-testament-quizzes" className="text-blue-400 hover:text-blue-300 transition-colors py-1 font-medium">
                View All →
              </Link>
            </div>
          </div>

          {/* New Testament */}
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-3">New Testament</h4>
            <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 text-xs">
              {newTestamentBooks.map((book) => (
                <Link 
                  key={book.id} 
                  href={`/${book.slug}-quiz`}
                  className="text-gray-300 hover:text-white transition-colors py-1"
                >
                  {book.name}
                </Link>
              ))}
              <Link href="/new-testament-quizzes" className="text-blue-400 hover:text-blue-300 transition-colors py-1 font-medium">
                View All →
              </Link>
            </div>
          </div>
        </div>

        {/* Quiz Categories */}
        <div className="mt-8 pt-6 border-t border-gray-700">
          <h4 className="text-sm font-medium text-gray-400 mb-3">Quiz Categories</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 text-xs">
            <Link href="/easy-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Easy Bible Quiz</Link>
            <Link href="/hard-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Hard Bible Quiz</Link>
            <Link href="/kids-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Kids Bible Quiz</Link>
            <Link href="/youth-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Youth Bible Quiz</Link>
            <Link href="/adult-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Adult Bible Quiz</Link>
            <Link href="/multiple-choice-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Multiple Choice</Link>
            <Link href="/true-false-bible-quiz" className="text-gray-300 hover:text-white transition-colors">True/False</Link>
            <Link href="/fill-in-blank-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Fill in Blank</Link>
            <Link href="/printable-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Printable Quiz</Link>
            <Link href="/interactive-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Interactive Quiz</Link>
            <Link href="/daily-bible-quiz" className="text-gray-300 hover:text-white transition-colors">Daily Bible Quiz</Link>
            <Link href="/bible-quiz-games" className="text-gray-300 hover:text-white transition-colors">Quiz Games</Link>
          </div>
        </div>
      </div>

      {/* Bottom Footer Bar */}
      <div className="border-t border-gray-700 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-400">
            <div className="mb-2 sm:mb-0">
              © 2025 SalvationCall | 
              <Link href="/privacy-policy" className="hover:text-white ml-1">Privacy Policy</Link> | 
              <Link href="/terms-of-service" className="hover:text-white ml-1">Terms</Link> | 
              <Link href="/contact" className="hover:text-white ml-1">Contact</Link> | 
              <Link href="/sitemap" className="hover:text-white ml-1">Sitemap</Link>
            </div>
            <div className="flex space-x-4">
              <span className="text-gray-500">Social:</span>
              <a href="#" className="hover:text-white transition-colors">Facebook</a>
              <a href="#" className="hover:text-white transition-colors">Twitter</a>
              <a href="#" className="hover:text-white transition-colors">YouTube</a>
              <a href="#" className="hover:text-white transition-colors">Instagram</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
