import { Quiz, Quiz<PERSON><PERSON><PERSON>, Q<PERSON><PERSON><PERSON><PERSON>, <PERSON>rAnswer, QuizFilters, DifficultyLevel, QuizCategory, Testament } from '../types/quiz';
import { BIBLE_BOOKS } from '../data/bible-books';
import { BIBLE_CHARACTERS } from '../data/bible-characters';
import { QUIZ_THEMES } from '../data/quiz-themes';

/**
 * Generate a slug from a string (book name, character name, etc.)
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * Generate quiz URL based on type and parameters
 */
export function generateQuizUrl(quiz: Quiz): string {
  if (quiz.isChapterQuiz && quiz.bookName && quiz.chapterNumber) {
    const bookSlug = generateSlug(quiz.bookName);
    return `/${bookSlug}-${quiz.chapterNumber}-quiz/`;
  }
  
  if (quiz.isBookQuiz && quiz.bookName) {
    const bookSlug = generateSlug(quiz.bookName);
    return `/${bookSlug}-quiz/`;
  }
  
  if (quiz.characterName) {
    const characterSlug = generateSlug(quiz.characterName);
    return `/${characterSlug}-quiz/`;
  }
  
  if (quiz.theme) {
    const themeSlug = generateSlug(quiz.theme);
    return `/${themeSlug}-quiz/`;
  }
  
  return `/${quiz.slug}/`;
}

/**
 * Calculate quiz score and percentage
 */
export function calculateQuizScore(answers: UserAnswer[], questions: QuizQuestion[]): {
  score: number;
  percentage: number;
  correctAnswers: number;
  incorrectAnswers: number;
} {
  const correctAnswers = answers.filter(answer => answer.isCorrect).length;
  const incorrectAnswers = answers.length - correctAnswers;
  const percentage = Math.round((correctAnswers / questions.length) * 100);
  
  return {
    score: correctAnswers,
    percentage,
    correctAnswers,
    incorrectAnswers
  };
}

/**
 * Get performance message based on score percentage
 */
export function getPerformanceMessage(percentage: number): string {
  if (percentage >= 90) return "Outstanding! You're a Bible scholar!";
  if (percentage >= 80) return "Excellent! You know your Scripture well!";
  if (percentage >= 70) return "Good job! Keep studying to improve!";
  if (percentage >= 60) return "Not bad! Review the study guide for better results.";
  return "Keep learning! Try our study guide first.";
}

/**
 * Generate SEO title for quiz pages
 */
export function generateSEOTitle(quiz: Quiz): string {
  if (quiz.isChapterQuiz && quiz.bookName && quiz.chapterNumber) {
    return `${quiz.bookName} Chapter ${quiz.chapterNumber} Quiz - Test Your Bible Knowledge | SalvationCall`;
  }
  
  if (quiz.isBookQuiz && quiz.bookName) {
    return `${quiz.bookName} Quiz - Test Your Bible Knowledge | SalvationCall`;
  }
  
  if (quiz.characterName) {
    return `${quiz.characterName} Bible Quiz - Test Your Knowledge | SalvationCall`;
  }
  
  if (quiz.theme) {
    return `${quiz.theme} Bible Quiz - Scripture Knowledge Test | SalvationCall`;
  }
  
  return `${quiz.title} - Bible Quiz | SalvationCall`;
}

/**
 * Generate SEO description for quiz pages
 */
export function generateSEODescription(quiz: Quiz): string {
  const questionCount = quiz.totalQuestions;
  const timeEstimate = quiz.estimatedTime;
  
  if (quiz.isChapterQuiz && quiz.bookName && quiz.chapterNumber) {
    return `Test your knowledge of ${quiz.bookName} chapter ${quiz.chapterNumber} with this interactive Bible quiz. ${questionCount} questions covering key verses, characters, and themes. Free instant results!`;
  }
  
  if (quiz.isBookQuiz && quiz.bookName) {
    return `Challenge yourself with this comprehensive ${quiz.bookName} Bible quiz! ${questionCount} questions covering the entire book with detailed explanations. Test your knowledge now!`;
  }
  
  if (quiz.characterName) {
    return `Challenge yourself with this ${quiz.characterName} Bible quiz! ${questionCount} questions about their life, faith journey, and key biblical moments. Test your knowledge now!`;
  }
  
  if (quiz.theme) {
    return `Explore ${quiz.theme} in Scripture with this comprehensive Bible quiz. ${questionCount} questions from Old and New Testament passages. Perfect for Bible study groups!`;
  }
  
  return `${quiz.description} ${questionCount} questions, approximately ${timeEstimate} minutes.`;
}

/**
 * Filter quizzes based on criteria
 */
export function filterQuizzes(quizzes: Quiz[], filters: QuizFilters): Quiz[] {
  return quizzes.filter(quiz => {
    if (filters.category && quiz.category !== filters.category) return false;
    if (filters.difficulty && quiz.difficulty !== filters.difficulty) return false;
    if (filters.testament && quiz.testament !== filters.testament) return false;
    if (filters.ageGroup && quiz.ageGroup !== filters.ageGroup) return false;
    if (filters.bookName && quiz.bookName !== filters.bookName) return false;
    if (filters.characterName && quiz.characterName !== filters.characterName) return false;
    if (filters.theme && quiz.theme !== filters.theme) return false;
    
    if (filters.tags && filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => 
        quiz.tags.some(quizTag => quizTag.toLowerCase().includes(tag.toLowerCase()))
      );
      if (!hasMatchingTag) return false;
    }
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = [
        quiz.title,
        quiz.description,
        quiz.bookName,
        quiz.characterName,
        quiz.theme,
        ...quiz.tags
      ].filter(Boolean).join(' ').toLowerCase();
      
      if (!searchableText.includes(searchTerm)) return false;
    }
    
    return true;
  });
}

/**
 * Generate all possible chapter quiz slugs for a book
 */
export function generateChapterQuizSlugs(bookSlug: string, chapterCount: number): string[] {
  const slugs: string[] = [];
  for (let i = 1; i <= chapterCount; i++) {
    slugs.push(`${bookSlug}-${i}-quiz`);
  }
  return slugs;
}

/**
 * Generate all possible book quiz slugs
 */
export function generateBookQuizSlugs(): string[] {
  return BIBLE_BOOKS.map(book => `${book.slug}-quiz`);
}

/**
 * Generate all possible character quiz slugs
 */
export function generateCharacterQuizSlugs(): string[] {
  return BIBLE_CHARACTERS.map(character => `${character.slug}-quiz`);
}

/**
 * Generate all possible theme quiz slugs
 */
export function generateThemeQuizSlugs(): string[] {
  return QUIZ_THEMES.map(theme => `${theme.slug}-quiz`);
}

/**
 * Parse quiz slug to determine type and parameters
 */
export function parseQuizSlug(slug: string): {
  type: 'chapter' | 'book' | 'character' | 'theme' | 'unknown';
  bookSlug?: string;
  chapterNumber?: number;
  characterSlug?: string;
  themeSlug?: string;
} {
  // Remove trailing -quiz if present
  const baseSlug = slug.replace(/-quiz$/, '');
  
  // Check for chapter quiz pattern (book-chapter-quiz)
  const chapterMatch = baseSlug.match(/^(.+)-(\d+)$/);
  if (chapterMatch) {
    const [, bookSlug, chapterStr] = chapterMatch;
    const chapterNumber = parseInt(chapterStr, 10);
    const book = BIBLE_BOOKS.find(b => b.slug === bookSlug);
    if (book && chapterNumber <= book.chapters) {
      return {
        type: 'chapter',
        bookSlug,
        chapterNumber
      };
    }
  }
  
  // Check for book quiz
  const book = BIBLE_BOOKS.find(b => b.slug === baseSlug);
  if (book) {
    return {
      type: 'book',
      bookSlug: baseSlug
    };
  }
  
  // Check for character quiz
  const character = BIBLE_CHARACTERS.find(c => c.slug === baseSlug);
  if (character) {
    return {
      type: 'character',
      characterSlug: baseSlug
    };
  }
  
  // Check for theme quiz
  const theme = QUIZ_THEMES.find(t => t.slug === baseSlug);
  if (theme) {
    return {
      type: 'theme',
      themeSlug: baseSlug
    };
  }
  
  return { type: 'unknown' };
}

/**
 * Shuffle array (for randomizing questions or answers)
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Format time duration in minutes and seconds
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes === 0) {
    return `${remainingSeconds}s`;
  }
  
  if (remainingSeconds === 0) {
    return `${minutes}m`;
  }
  
  return `${minutes}m ${remainingSeconds}s`;
}

/**
 * Get estimated reading time for quiz description
 */
export function getEstimatedReadingTime(text: string): number {
  const wordsPerMinute = 200;
  const wordCount = text.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * Validate quiz data structure
 */
export function validateQuiz(quiz: Quiz): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!quiz.id) errors.push('Quiz ID is required');
  if (!quiz.title) errors.push('Quiz title is required');
  if (!quiz.slug) errors.push('Quiz slug is required');
  if (!quiz.questions || quiz.questions.length === 0) errors.push('Quiz must have questions');
  if (quiz.totalQuestions !== quiz.questions.length) errors.push('Total questions count mismatch');
  
  // Validate question count requirements
  if (quiz.isChapterQuiz && (quiz.questions.length < 16 || quiz.questions.length > 20)) {
    errors.push('Chapter quizzes must have 16-20 questions');
  }
  
  if (quiz.isBookQuiz && (quiz.questions.length < 20 || quiz.questions.length > 25)) {
    errors.push('Book quizzes must have 20-25 questions');
  }
  
  // Validate each question
  quiz.questions.forEach((question, index) => {
    if (!question.id) errors.push(`Question ${index + 1}: ID is required`);
    if (!question.question) errors.push(`Question ${index + 1}: Question text is required`);
    if (!question.answers || question.answers.length === 0) errors.push(`Question ${index + 1}: Answers are required`);
    if (!question.correctAnswerId) errors.push(`Question ${index + 1}: Correct answer ID is required`);
    
    const correctAnswer = question.answers.find(a => a.id === question.correctAnswerId);
    if (!correctAnswer) errors.push(`Question ${index + 1}: Correct answer not found in answers array`);
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
