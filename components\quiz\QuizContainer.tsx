'use client';

import { useState, useEffect } from 'react';
import { Quiz, UserAnswer, QuizResult } from '../../lib/types/quiz';
import QuizHeader from './QuizHeader';
import QuizQuestion from './QuizQuestion';
import QuizResults from './QuizResults';
import QuizProgress from './QuizProgress';
import { calculateQuizScore } from '../../lib/utils/quiz-utils';

interface QuizContainerProps {
  quiz: Quiz;
}

const QuizContainer = ({ quiz }: QuizContainerProps) => {
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);
  const [isCompleted, setIsCompleted] = useState(false);
  const [quizResult, setQuizResult] = useState<QuizResult | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);

  useEffect(() => {
    setStartTime(new Date());
  }, []);

  const handleAnswerSelect = (questionId: string, answerId: string) => {
    const question = quiz.questions.find(q => q.id === questionId);
    if (!question) return;

    const isCorrect = question.correctAnswerId === answerId;
    
    const newAnswer: UserAnswer = {
      questionId,
      answerId,
      isCorrect,
      timeSpent: 0, // Could be calculated if needed
      points: isCorrect ? (question.points || 1) : 0
    };

    setUserAnswers(prev => {
      const filtered = prev.filter(a => a.questionId !== questionId);
      return [...filtered, newAnswer];
    });
  };

  const handleSubmitQuiz = () => {
    if (userAnswers.length !== quiz.questions.length) {
      alert('Please answer all questions before submitting.');
      return;
    }

    const endTime = new Date();
    const timeSpent = startTime ? Math.floor((endTime.getTime() - startTime.getTime()) / 1000) : 0;
    
    const scoreData = calculateQuizScore(userAnswers, quiz.questions);
    
    const result: QuizResult = {
      quizId: quiz.id,
      userAnswers,
      score: scoreData.score,
      totalQuestions: quiz.questions.length,
      percentage: scoreData.percentage,
      timeSpent,
      completedAt: endTime,
      passed: scoreData.percentage >= 70 // 70% passing grade
    };

    setQuizResult(result);
    setIsCompleted(true);

    // Scroll to results
    setTimeout(() => {
      const resultsElement = document.getElementById('quiz-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  const resetQuiz = () => {
    setUserAnswers([]);
    setIsCompleted(false);
    setQuizResult(null);
    setStartTime(new Date());
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getAnsweredCount = () => {
    return userAnswers.length;
  };

  const isQuestionAnswered = (questionId: string) => {
    return userAnswers.some(answer => answer.questionId === questionId);
  };

  const getUserAnswer = (questionId: string) => {
    return userAnswers.find(answer => answer.questionId === questionId);
  };

  if (isCompleted && quizResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <QuizResults 
          quiz={quiz} 
          result={quizResult} 
          onRetakeQuiz={resetQuiz}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Quiz Header */}
      <QuizHeader quiz={quiz} />

      {/* Progress Bar */}
      <QuizProgress 
        totalQuestions={quiz.questions.length}
        answeredQuestions={getAnsweredCount()}
      />

      {/* Main Quiz Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Quiz Instructions - Simplified */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4 text-center">
            {quiz.title}
          </h2>
          <div className="text-center text-gray-600 mb-4">
            <p>{quiz.questions.length} questions • {quiz.estimatedTime} minutes • Answer all questions to submit</p>
          </div>
          <div className="text-center">
            <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
              📝 {getAnsweredCount()} of {quiz.questions.length} answered
            </span>
          </div>
        </div>

        {/* Questions - Clean Linear Layout */}
        <div className="space-y-6">
          {quiz.questions.map((question, index) => (
            <QuizQuestion
              key={question.id}
              question={question}
              questionNumber={index + 1}
              userAnswer={getUserAnswer(question.id)}
              onAnswerSelect={handleAnswerSelect}
              showResults={false}
            />
          ))}
        </div>

        {/* Submit Section */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Submit?</h3>
          <p className="text-gray-600 mb-6">
            You have answered {getAnsweredCount()} of {quiz.questions.length} questions.
          </p>
          
          {getAnsweredCount() === quiz.questions.length ? (
            <button
              onClick={handleSubmitQuiz}
              className="bg-green-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-green-700 transition-colors"
            >
              Submit Quiz & See Results
            </button>
          ) : (
            <div>
              <button
                disabled
                className="bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg cursor-not-allowed"
              >
                Answer All Questions to Submit
              </button>
              <p className="text-sm text-gray-500 mt-2">
                {quiz.questions.length - getAnsweredCount()} questions remaining
              </p>
            </div>
          )}
        </div>

        {/* Study Resources */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">📚 Study Resources</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Before Taking Quiz:</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• <a href="#" className="hover:underline">Study {quiz.bookName} Guide</a></li>
                <li>• <a href="#" className="hover:underline">Read {quiz.bookName} Summary</a></li>
                <li>• <a href="#" className="hover:underline">Key Verses to Memorize</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Related Quizzes:</h4>
              <ul className="space-y-1 text-blue-700">
                {quiz.isBookQuiz && (
                  <li>• <a href={`/${quiz.bookName?.toLowerCase()}-chapters`} className="hover:underline">{quiz.bookName} Chapter Quizzes</a></li>
                )}
                <li>• <a href="/old-testament-quizzes" className="hover:underline">Old Testament Quizzes</a></li>
                <li>• <a href="/bible-quizzes" className="hover:underline">All Bible Quizzes</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizContainer;
