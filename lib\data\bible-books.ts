import { BibleBook, Testament } from '../types/quiz';

export const BIBLE_BOOKS: BibleBook[] = [
  // Old Testament
  { id: 'genesis', name: 'Genesis', slug: 'genesis', testament: 'old', order: 1, chapters: 50, abbreviation: 'Gen', description: 'The book of beginnings - creation, fall, and the patriarchs' },
  { id: 'exodus', name: 'Exodus', slug: 'exodus', testament: 'old', order: 2, chapters: 40, abbreviation: 'Exo', description: 'The deliverance of Israel from Egypt and the giving of the Law' },
  { id: 'leviticus', name: '<PERSON><PERSON>', slug: 'leviticus', testament: 'old', order: 3, chapters: 27, abbreviation: '<PERSON>', description: 'Laws for worship, sacrifice, and holy living' },
  { id: 'numbers', name: 'Numbers', slug: 'numbers', testament: 'old', order: 4, chapters: 36, abbreviation: 'Num', description: 'Israel\'s wilderness wanderings and preparation for the Promised Land' },
  { id: 'deuteronomy', name: 'Deuteronomy', slug: 'deuteronomy', testament: 'old', order: 5, chapters: 34, abbreviation: 'Deu', description: '<PERSON>\' final speeches and the renewal of the covenant' },
  { id: 'joshua', name: '<PERSON>', slug: 'joshua', testament: 'old', order: 6, chapters: 24, abbreviation: 'Jos', description: 'The conquest and settlement of the Promised Land' },
  { id: 'judges', name: 'Judges', slug: 'judges', testament: 'old', order: 7, chapters: 21, abbreviation: 'Jdg', description: 'The cycle of sin, oppression, and deliverance in early Israel' },
  { id: 'ruth', name: 'Ruth', slug: 'ruth', testament: 'old', order: 8, chapters: 4, abbreviation: 'Rut', description: 'A story of loyalty, love, and redemption' },
  { id: '1-samuel', name: '1 Samuel', slug: '1-samuel', testament: 'old', order: 9, chapters: 31, abbreviation: '1Sa', description: 'The transition from judges to kings - Samuel, Saul, and David' },
  { id: '2-samuel', name: '2 Samuel', slug: '2-samuel', testament: 'old', order: 10, chapters: 24, abbreviation: '2Sa', description: 'David\'s reign as king of Israel' },
  { id: '1-kings', name: '1 Kings', slug: '1-kings', testament: 'old', order: 11, chapters: 22, abbreviation: '1Ki', description: 'Solomon\'s reign and the division of the kingdom' },
  { id: '2-kings', name: '2 Kings', slug: '2-kings', testament: 'old', order: 12, chapters: 25, abbreviation: '2Ki', description: 'The decline and fall of Israel and Judah' },
  { id: '1-chronicles', name: '1 Chronicles', slug: '1-chronicles', testament: 'old', order: 13, chapters: 29, abbreviation: '1Ch', description: 'A priestly perspective on Israel\'s history from Adam to David' },
  { id: '2-chronicles', name: '2 Chronicles', slug: '2-chronicles', testament: 'old', order: 14, chapters: 36, abbreviation: '2Ch', description: 'The history of Judah from Solomon to the exile' },
  { id: 'ezra', name: 'Ezra', slug: 'ezra', testament: 'old', order: 15, chapters: 10, abbreviation: 'Ezr', description: 'The return from exile and rebuilding of the temple' },
  { id: 'nehemiah', name: 'Nehemiah', slug: 'nehemiah', testament: 'old', order: 16, chapters: 13, abbreviation: 'Neh', description: 'Rebuilding Jerusalem\'s walls and spiritual renewal' },
  { id: 'esther', name: 'Esther', slug: 'esther', testament: 'old', order: 17, chapters: 10, abbreviation: 'Est', description: 'God\'s providence in preserving His people' },
  { id: 'job', name: 'Job', slug: 'job', testament: 'old', order: 18, chapters: 42, abbreviation: 'Job', description: 'The problem of suffering and God\'s sovereignty' },
  { id: 'psalms', name: 'Psalms', slug: 'psalms', testament: 'old', order: 19, chapters: 150, abbreviation: 'Psa', description: 'Songs of worship, praise, and prayer' },
  { id: 'proverbs', name: 'Proverbs', slug: 'proverbs', testament: 'old', order: 20, chapters: 31, abbreviation: 'Pro', description: 'Wisdom for daily living' },
  { id: 'ecclesiastes', name: 'Ecclesiastes', slug: 'ecclesiastes', testament: 'old', order: 21, chapters: 12, abbreviation: 'Ecc', description: 'The search for meaning and purpose in life' },
  { id: 'song-of-solomon', name: 'Song of Solomon', slug: 'song-of-solomon', testament: 'old', order: 22, chapters: 8, abbreviation: 'SoS', description: 'A celebration of love and marriage' },
  { id: 'isaiah', name: 'Isaiah', slug: 'isaiah', testament: 'old', order: 23, chapters: 66, abbreviation: 'Isa', description: 'Prophecies of judgment and salvation, including the Messiah' },
  { id: 'jeremiah', name: 'Jeremiah', slug: 'jeremiah', testament: 'old', order: 24, chapters: 52, abbreviation: 'Jer', description: 'The weeping prophet\'s call to repentance' },
  { id: 'lamentations', name: 'Lamentations', slug: 'lamentations', testament: 'old', order: 25, chapters: 5, abbreviation: 'Lam', description: 'Mourning over Jerusalem\'s destruction' },
  { id: 'ezekiel', name: 'Ezekiel', slug: 'ezekiel', testament: 'old', order: 26, chapters: 48, abbreviation: 'Eze', description: 'Visions of God\'s glory and restoration' },
  { id: 'daniel', name: 'Daniel', slug: 'daniel', testament: 'old', order: 27, chapters: 12, abbreviation: 'Dan', description: 'Faithfulness in exile and apocalyptic visions' },
  { id: 'hosea', name: 'Hosea', slug: 'hosea', testament: 'old', order: 28, chapters: 14, abbreviation: 'Hos', description: 'God\'s unfailing love despite Israel\'s unfaithfulness' },
  { id: 'joel', name: 'Joel', slug: 'joel', testament: 'old', order: 29, chapters: 3, abbreviation: 'Joe', description: 'The Day of the Lord and the outpouring of the Spirit' },
  { id: 'amos', name: 'Amos', slug: 'amos', testament: 'old', order: 30, chapters: 9, abbreviation: 'Amo', description: 'A call for justice and righteousness' },
  { id: 'obadiah', name: 'Obadiah', slug: 'obadiah', testament: 'old', order: 31, chapters: 1, abbreviation: 'Oba', description: 'Judgment against Edom and restoration of Israel' },
  { id: 'jonah', name: 'Jonah', slug: 'jonah', testament: 'old', order: 32, chapters: 4, abbreviation: 'Jon', description: 'God\'s mercy extends to all nations' },
  { id: 'micah', name: 'Micah', slug: 'micah', testament: 'old', order: 33, chapters: 7, abbreviation: 'Mic', description: 'Justice, mercy, and the coming Messiah' },
  { id: 'nahum', name: 'Nahum', slug: 'nahum', testament: 'old', order: 34, chapters: 3, abbreviation: 'Nah', description: 'The fall of Nineveh and God\'s justice' },
  { id: 'habakkuk', name: 'Habakkuk', slug: 'habakkuk', testament: 'old', order: 35, chapters: 3, abbreviation: 'Hab', description: 'Wrestling with God\'s justice and living by faith' },
  { id: 'zephaniah', name: 'Zephaniah', slug: 'zephaniah', testament: 'old', order: 36, chapters: 3, abbreviation: 'Zep', description: 'The Day of the Lord and restoration' },
  { id: 'haggai', name: 'Haggai', slug: 'haggai', testament: 'old', order: 37, chapters: 2, abbreviation: 'Hag', description: 'Rebuilding the temple and God\'s priorities' },
  { id: 'zechariah', name: 'Zechariah', slug: 'zechariah', testament: 'old', order: 38, chapters: 14, abbreviation: 'Zec', description: 'Visions of restoration and the coming King' },
  { id: 'malachi', name: 'Malachi', slug: 'malachi', testament: 'old', order: 39, chapters: 4, abbreviation: 'Mal', description: 'The final Old Testament prophet\'s call to faithfulness' },

  // New Testament
  { id: 'matthew', name: 'Matthew', slug: 'matthew', testament: 'new', order: 40, chapters: 28, abbreviation: 'Mat', description: 'Jesus as the promised Messiah and King' },
  { id: 'mark', name: 'Mark', slug: 'mark', testament: 'new', order: 41, chapters: 16, abbreviation: 'Mar', description: 'Jesus as the suffering Servant' },
  { id: 'luke', name: 'Luke', slug: 'luke', testament: 'new', order: 42, chapters: 24, abbreviation: 'Luk', description: 'Jesus as the perfect Son of Man' },
  { id: 'john', name: 'John', slug: 'john', testament: 'new', order: 43, chapters: 21, abbreviation: 'Joh', description: 'Jesus as the Son of God and eternal life' },
  { id: 'acts', name: 'Acts', slug: 'acts', testament: 'new', order: 44, chapters: 28, abbreviation: 'Act', description: 'The birth and growth of the early church' },
  { id: 'romans', name: 'Romans', slug: 'romans', testament: 'new', order: 45, chapters: 16, abbreviation: 'Rom', description: 'The gospel of God\'s righteousness and salvation' },
  { id: '1-corinthians', name: '1 Corinthians', slug: '1-corinthians', testament: 'new', order: 46, chapters: 16, abbreviation: '1Co', description: 'Addressing problems in the Corinthian church' },
  { id: '2-corinthians', name: '2 Corinthians', slug: '2-corinthians', testament: 'new', order: 47, chapters: 13, abbreviation: '2Co', description: 'Paul\'s defense of his apostolic ministry' },
  { id: 'galatians', name: 'Galatians', slug: 'galatians', testament: 'new', order: 48, chapters: 6, abbreviation: 'Gal', description: 'Freedom from the law through faith in Christ' },
  { id: 'ephesians', name: 'Ephesians', slug: 'ephesians', testament: 'new', order: 49, chapters: 6, abbreviation: 'Eph', description: 'The church as the body of Christ' },
  { id: 'philippians', name: 'Philippians', slug: 'philippians', testament: 'new', order: 50, chapters: 4, abbreviation: 'Phi', description: 'Joy and unity in Christ' },
  { id: 'colossians', name: 'Colossians', slug: 'colossians', testament: 'new', order: 51, chapters: 4, abbreviation: 'Col', description: 'The supremacy and sufficiency of Christ' },
  { id: '1-thessalonians', name: '1 Thessalonians', slug: '1-thessalonians', testament: 'new', order: 52, chapters: 5, abbreviation: '1Th', description: 'Living in light of Christ\'s return' },
  { id: '2-thessalonians', name: '2 Thessalonians', slug: '2-thessalonians', testament: 'new', order: 53, chapters: 3, abbreviation: '2Th', description: 'Clarifications about the Second Coming' },
  { id: '1-timothy', name: '1 Timothy', slug: '1-timothy', testament: 'new', order: 54, chapters: 6, abbreviation: '1Ti', description: 'Instructions for church leadership and conduct' },
  { id: '2-timothy', name: '2 Timothy', slug: '2-timothy', testament: 'new', order: 55, chapters: 4, abbreviation: '2Ti', description: 'Paul\'s final charge to Timothy' },
  { id: 'titus', name: 'Titus', slug: 'titus', testament: 'new', order: 56, chapters: 3, abbreviation: 'Tit', description: 'Qualifications for church leaders and sound doctrine' },
  { id: 'philemon', name: 'Philemon', slug: 'philemon', testament: 'new', order: 57, chapters: 1, abbreviation: 'Phm', description: 'Forgiveness and reconciliation in Christ' },
  { id: 'hebrews', name: 'Hebrews', slug: 'hebrews', testament: 'new', order: 58, chapters: 13, abbreviation: 'Heb', description: 'The superiority of Christ and the new covenant' },
  { id: 'james', name: 'James', slug: 'james', testament: 'new', order: 59, chapters: 5, abbreviation: 'Jam', description: 'Practical wisdom for Christian living' },
  { id: '1-peter', name: '1 Peter', slug: '1-peter', testament: 'new', order: 60, chapters: 5, abbreviation: '1Pe', description: 'Hope and perseverance in suffering' },
  { id: '2-peter', name: '2 Peter', slug: '2-peter', testament: 'new', order: 61, chapters: 3, abbreviation: '2Pe', description: 'Warning against false teachers and the promise of Christ\'s return' },
  { id: '1-john', name: '1 John', slug: '1-john', testament: 'new', order: 62, chapters: 5, abbreviation: '1Jo', description: 'Assurance of salvation and the love of God' },
  { id: '2-john', name: '2 John', slug: '2-john', testament: 'new', order: 63, chapters: 1, abbreviation: '2Jo', description: 'Walking in truth and love' },
  { id: '3-john', name: '3 John', slug: '3-john', testament: 'new', order: 64, chapters: 1, abbreviation: '3Jo', description: 'Hospitality and supporting fellow workers' },
  { id: 'jude', name: 'Jude', slug: 'jude', testament: 'new', order: 65, chapters: 1, abbreviation: 'Jud', description: 'Contending for the faith against false teachers' },
  { id: 'revelation', name: 'Revelation', slug: 'revelation', testament: 'new', order: 66, chapters: 22, abbreviation: 'Rev', description: 'The ultimate victory of Christ and the new creation' }
];

export const OLD_TESTAMENT_BOOKS = BIBLE_BOOKS.filter(book => book.testament === 'old');
export const NEW_TESTAMENT_BOOKS = BIBLE_BOOKS.filter(book => book.testament === 'new');

export function getBibleBookBySlug(slug: string): BibleBook | undefined {
  return BIBLE_BOOKS.find(book => book.slug === slug);
}

export function getBibleBookById(id: string): BibleBook | undefined {
  return BIBLE_BOOKS.find(book => book.id === id);
}

export function getBooksByTestament(testament: Testament): BibleBook[] {
  return BIBLE_BOOKS.filter(book => book.testament === testament);
}
