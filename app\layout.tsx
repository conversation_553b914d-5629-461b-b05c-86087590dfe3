import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import Header from "../components/layout/Header";
import Footer from "../components/layout/Footer";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Bible Quiz Hub - Test Your Scripture Knowledge | SalvationCall",
  description: "Comprehensive Bible quizzes for all 66 books with 16-25 questions each. Test your knowledge of Scripture with interactive quizzes covering Old Testament, New Testament, characters, and themes.",
  keywords: ["bible quiz", "scripture test", "bible knowledge", "christian quiz", "bible study", "old testament quiz", "new testament quiz"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
