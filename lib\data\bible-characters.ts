import { BibleCharacter, Testament } from '../types/quiz';

export const BIBLE_CHARACTERS: BibleCharacter[] = [
  // Old Testament Major Characters
  {
    id: 'abraham',
    name: '<PERSON>',
    slug: 'abraham',
    testament: 'old',
    description: 'The father of faith, called by God to leave his homeland and become the father of many nations.',
    keyVerses: ['Genesis 12:1-3', 'Romans 4:16', 'Hebrews 11:8-12'],
    relatedBooks: ['Genesis', 'Romans', 'Hebrews', 'Galatians'],
    significance: 'Father of the Jewish nation and example of faith for all believers'
  },
  {
    id: 'moses',
    name: '<PERSON>',
    slug: 'moses',
    testament: 'old',
    description: 'The great lawgiver and deliverer of Israel from Egyptian bondage.',
    keyVerses: ['Exodus 3:1-15', 'Deuteronomy 34:10-12', 'Hebrews 11:24-29'],
    relatedBooks: ['Exodus', 'Leviticus', 'Numbers', 'Deuteronomy'],
    significance: 'Deliverer of Israel and mediator of the Old Covenant'
  },
  {
    id: 'david',
    name: '<PERSON>',
    slug: 'david',
    testament: 'old',
    description: 'The shepherd boy who became king, a man after God\'s own heart.',
    keyVerses: ['1 Samuel 16:7', '2 <PERSON> 7:12-16', '<PERSON>salm 23', 'Acts 13:22'],
    relatedBooks: ['1 <PERSON>', '2 <PERSON>', '1 Chronicles', 'Psalms'],
    significance: 'King of Israel and ancestor of <PERSON> Christ'
  },
  {
    id: 'solomon',
    name: '<PERSON>',
    slug: 'solomon',
    testament: 'old',
    description: 'The wisest king of Israel who built the first temple.',
    keyVerses: ['1 <PERSON> 3:5-14', '1 Kings 4:29-34', 'Ecclesiastes 12:13'],
    relatedBooks: ['1 Kings', '2 Chronicles', 'Proverbs', 'Ecclesiastes', 'Song of Solomon'],
    significance: 'Wisest man who ever lived and builder of the temple'
  },
  {
    id: 'noah',
    name: 'Noah',
    slug: 'noah',
    testament: 'old',
    description: 'The righteous man who built the ark and survived the great flood.',
    keyVerses: ['Genesis 6:9', 'Genesis 7:1', 'Hebrews 11:7', '2 Peter 2:5'],
    relatedBooks: ['Genesis', 'Matthew', 'Luke', 'Hebrews', '1 Peter', '2 Peter'],
    significance: 'Preacher of righteousness and survivor of God\'s judgment'
  },
  {
    id: 'joseph',
    name: 'Joseph',
    slug: 'joseph',
    testament: 'old',
    description: 'The dreamer who became second in command in Egypt and saved his family.',
    keyVerses: ['Genesis 37:5-11', 'Genesis 45:4-8', 'Genesis 50:20'],
    relatedBooks: ['Genesis', 'Acts', 'Hebrews'],
    significance: 'Example of forgiveness and God\'s providence'
  },
  {
    id: 'daniel',
    name: 'Daniel',
    slug: 'daniel',
    testament: 'old',
    description: 'The prophet who remained faithful to God in Babylonian exile.',
    keyVerses: ['Daniel 1:8', 'Daniel 6:10', 'Daniel 9:23'],
    relatedBooks: ['Daniel', 'Matthew', 'Revelation'],
    significance: 'Example of faithfulness in a foreign culture'
  },
  {
    id: 'elijah',
    name: 'Elijah',
    slug: 'elijah',
    testament: 'old',
    description: 'The fiery prophet who confronted idolatry and was taken up to heaven.',
    keyVerses: ['1 Kings 18:21', '1 Kings 19:11-13', '2 Kings 2:11'],
    relatedBooks: ['1 Kings', '2 Kings', 'Malachi', 'Matthew', 'Luke', 'James'],
    significance: 'Prophet of fire who will return before the Day of the Lord'
  },
  {
    id: 'esther',
    name: 'Esther',
    slug: 'esther',
    testament: 'old',
    description: 'The Jewish queen who saved her people from destruction.',
    keyVerses: ['Esther 4:14', 'Esther 4:16'],
    relatedBooks: ['Esther'],
    significance: 'Example of courage and God\'s providence'
  },
  {
    id: 'ruth',
    name: 'Ruth',
    slug: 'ruth',
    testament: 'old',
    description: 'The Moabite woman who showed loyalty and became an ancestor of David.',
    keyVerses: ['Ruth 1:16-17', 'Ruth 4:13-17'],
    relatedBooks: ['Ruth', 'Matthew'],
    significance: 'Example of loyalty and inclusion in God\'s plan'
  },

  // New Testament Major Characters
  {
    id: 'jesus',
    name: 'Jesus Christ',
    slug: 'jesus',
    testament: 'new',
    description: 'The Son of God, Savior of the world, and central figure of Christianity.',
    keyVerses: ['John 3:16', 'John 14:6', 'Philippians 2:5-11', '1 Timothy 2:5'],
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', 'Romans', 'Hebrews', 'Revelation'],
    significance: 'The Messiah, Savior, and Lord of all creation'
  },
  {
    id: 'paul',
    name: 'Paul (Saul)',
    slug: 'paul',
    testament: 'new',
    description: 'The apostle to the Gentiles, former persecutor turned missionary.',
    keyVerses: ['Acts 9:1-19', 'Galatians 2:20', 'Philippians 3:7-14', '1 Timothy 1:15'],
    relatedBooks: ['Acts', 'Romans', '1 Corinthians', '2 Corinthians', 'Galatians', 'Ephesians', 'Philippians', 'Colossians', '1 Thessalonians', '2 Thessalonians', '1 Timothy', '2 Timothy', 'Titus', 'Philemon'],
    significance: 'Greatest missionary and theologian of the early church'
  },
  {
    id: 'peter',
    name: 'Peter',
    slug: 'peter',
    testament: 'new',
    description: 'The impulsive fisherman who became the rock of the early church.',
    keyVerses: ['Matthew 16:16-18', 'Luke 22:31-32', 'Acts 2:14-41', '1 Peter 5:1-4'],
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', '1 Peter', '2 Peter'],
    significance: 'Leader of the apostles and pillar of the early church'
  },
  {
    id: 'john',
    name: 'John',
    slug: 'john',
    testament: 'new',
    description: 'The beloved disciple, apostle of love, and author of five New Testament books.',
    keyVerses: ['John 13:23', 'John 21:20-24', '1 John 4:7-21'],
    relatedBooks: ['John', '1 John', '2 John', '3 John', 'Revelation'],
    significance: 'The apostle of love and author of the Gospel of John'
  },
  {
    id: 'mary-mother-of-jesus',
    name: 'Mary (Mother of Jesus)',
    slug: 'mary',
    testament: 'new',
    description: 'The virgin chosen by God to be the mother of Jesus Christ.',
    keyVerses: ['Luke 1:26-38', 'Luke 1:46-55', 'John 19:25-27'],
    relatedBooks: ['Matthew', 'Luke', 'John', 'Acts'],
    significance: 'Mother of Jesus and example of faith and obedience'
  },
  {
    id: 'john-the-baptist',
    name: 'John the Baptist',
    slug: 'john-the-baptist',
    testament: 'new',
    description: 'The forerunner of Christ who prepared the way for Jesus\' ministry.',
    keyVerses: ['Matthew 3:1-17', 'John 1:29', 'Luke 7:28'],
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    significance: 'The forerunner who prepared the way for Christ'
  },
  {
    id: 'stephen',
    name: 'Stephen',
    slug: 'stephen',
    testament: 'new',
    description: 'The first Christian martyr, full of faith and the Holy Spirit.',
    keyVerses: ['Acts 6:5', 'Acts 7:54-60'],
    relatedBooks: ['Acts'],
    significance: 'First Christian martyr and example of forgiveness'
  },
  {
    id: 'barnabas',
    name: 'Barnabas',
    slug: 'barnabas',
    testament: 'new',
    description: 'The encourager who mentored Paul and Mark in ministry.',
    keyVerses: ['Acts 4:36-37', 'Acts 11:22-26', 'Acts 15:36-41'],
    relatedBooks: ['Acts', 'Galatians', 'Colossians'],
    significance: 'Son of encouragement and missionary companion'
  },
  {
    id: 'timothy',
    name: 'Timothy',
    slug: 'timothy',
    testament: 'new',
    description: 'Paul\'s spiritual son and faithful ministry partner.',
    keyVerses: ['Acts 16:1-3', '1 Timothy 4:12', '2 Timothy 1:5'],
    relatedBooks: ['Acts', '1 Corinthians', 'Philippians', '1 Thessalonians', '1 Timothy', '2 Timothy'],
    significance: 'Paul\'s spiritual son and faithful minister'
  },
  {
    id: 'lydia',
    name: 'Lydia',
    slug: 'lydia',
    testament: 'new',
    description: 'The businesswoman who became Paul\'s first convert in Europe.',
    keyVerses: ['Acts 16:11-15', 'Acts 16:40'],
    relatedBooks: ['Acts'],
    significance: 'First European convert and supporter of Paul\'s ministry'
  }
];

export const OLD_TESTAMENT_CHARACTERS = BIBLE_CHARACTERS.filter(char => char.testament === 'old');
export const NEW_TESTAMENT_CHARACTERS = BIBLE_CHARACTERS.filter(char => char.testament === 'new');

export function getBibleCharacterBySlug(slug: string): BibleCharacter | undefined {
  return BIBLE_CHARACTERS.find(char => char.slug === slug);
}

export function getBibleCharacterById(id: string): BibleCharacter | undefined {
  return BIBLE_CHARACTERS.find(char => char.id === id);
}

export function getCharactersByTestament(testament: Testament): BibleCharacter[] {
  return BIBLE_CHARACTERS.filter(char => char.testament === testament);
}
