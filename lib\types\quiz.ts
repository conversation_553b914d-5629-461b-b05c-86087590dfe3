// Core quiz types with strict TypeScript checking
export type QuestionType = 'multiple-choice' | 'true-false' | 'fill-blank' | 'matching' | 'ordering';

export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'beginner' | 'intermediate' | 'advanced';

export type QuizCategory = 
  | 'chapter' 
  | 'book' 
  | 'character' 
  | 'theme' 
  | 'difficulty' 
  | 'age-group' 
  | 'testament';

export type Testament = 'old' | 'new';

export type AgeGroup = 'kids' | 'youth' | 'adult' | 'family';

export interface QuizAnswer {
  id: string;
  text: string;
  isCorrect: boolean;
  explanation?: string;
}

export interface QuizQuestion {
  id: string;
  questionType: QuestionType;
  question: string;
  answers: QuizAnswer[];
  correctAnswerId: string;
  explanation: string;
  bibleReference?: string;
  difficulty: DifficultyLevel;
  points: number;
  timeLimit?: number; // in seconds
  hints?: string[];
}

export interface Quiz {
  id: string;
  title: string;
  slug: string;
  description: string;
  category: QuizCategory;
  subcategory?: string;
  questions: QuizQuestion[];
  totalQuestions: number;
  estimatedTime: number; // in minutes
  difficulty: DifficultyLevel;
  testament?: Testament;
  ageGroup?: AgeGroup;
  tags: string[];
  isBookQuiz: boolean;
  isChapterQuiz: boolean;
  bookName?: string;
  chapterNumber?: number;
  characterName?: string;
  theme?: string;
  createdAt: Date;
  updatedAt: Date;
  isPublished: boolean;
  completionCount: number;
  averageScore: number;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
}

export interface QuizResult {
  id: string;
  quizId: string;
  userId?: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  timeSpent: number; // in seconds
  completedAt: Date;
  answers: UserAnswer[];
  percentage: number;
}

export interface UserAnswer {
  questionId: string;
  selectedAnswerId: string;
  isCorrect: boolean;
  timeSpent: number; // in seconds
}

export interface QuizProgress {
  quizId: string;
  currentQuestionIndex: number;
  answers: UserAnswer[];
  startTime: Date;
  isCompleted: boolean;
}

export interface BibleBook {
  id: string;
  name: string;
  slug: string;
  testament: Testament;
  order: number;
  chapters: number;
  abbreviation: string;
  description: string;
}

export interface BibleCharacter {
  id: string;
  name: string;
  slug: string;
  testament: Testament;
  description: string;
  keyVerses: string[];
  relatedBooks: string[];
  significance: string;
}

export interface QuizTheme {
  id: string;
  name: string;
  slug: string;
  description: string;
  relatedBooks: string[];
  keyVerses: string[];
  difficulty: DifficultyLevel;
}

// Navigation and SEO types
export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  icon?: string;
}

export interface BreadcrumbItem {
  label: string;
  href: string;
  isCurrentPage?: boolean;
}

export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  jsonLd?: Record<string, any>;
}

// Component prop types
export interface QuizComponentProps {
  quiz: Quiz;
  onComplete: (result: QuizResult) => void;
  onProgress?: (progress: QuizProgress) => void;
  showProgress?: boolean;
  allowRetake?: boolean;
}

export interface QuestionComponentProps {
  question: QuizQuestion;
  questionIndex: number;
  totalQuestions: number;
  onAnswer: (answerId: string) => void;
  selectedAnswer?: string;
  showResult?: boolean;
  disabled?: boolean;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter and search types
export interface QuizFilters {
  category?: QuizCategory;
  difficulty?: DifficultyLevel;
  testament?: Testament;
  ageGroup?: AgeGroup;
  bookName?: string;
  characterName?: string;
  theme?: string;
  tags?: string[];
  search?: string;
}

export interface QuizSearchParams {
  q?: string;
  category?: string;
  difficulty?: string;
  testament?: string;
  page?: string;
  limit?: string;
  sort?: 'newest' | 'oldest' | 'popular' | 'difficulty' | 'alphabetical';
}

// Error types
export interface QuizError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Analytics types
export interface QuizAnalytics {
  quizId: string;
  views: number;
  completions: number;
  averageScore: number;
  averageTime: number;
  dropOffPoints: number[];
  popularAnswers: Record<string, number>;
}
