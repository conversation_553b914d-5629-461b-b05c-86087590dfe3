interface QuizProgressProps {
  totalQuestions: number;
  answeredQuestions: number;
}

const QuizProgress = ({ totalQuestions, answeredQuestions }: QuizProgressProps) => {
  const percentage = Math.round((answeredQuestions / totalQuestions) * 100);

  return (
    <div className="sticky top-16 z-40 bg-white shadow-md border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">
              Progress: {answeredQuestions} of {totalQuestions}
            </span>
            <span className="text-sm text-gray-500">
              ({percentage}% complete)
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="flex-1 max-w-md mx-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${percentage}%` }}
              />
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex items-center space-x-2">
            {percentage === 100 ? (
              <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                ✅ Ready to Submit
              </span>
            ) : (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                📝 In Progress
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizProgress;
