import type { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { getBibleBookBySlug } from '../../lib/data/bible-books';

export const metadata: Metadata = {
  title: 'Genesis Chapter Quizzes - Test Your Knowledge | SalvationCall',
  description: 'Explore all 50 chapters of Genesis with individual Bible quizzes. Each chapter quiz contains 16-20 questions covering key verses, characters, and themes from the book of beginnings.',
  keywords: ['genesis chapters', 'genesis quiz', 'bible quiz', 'genesis study', 'old testament quiz', 'creation quiz', 'abraham quiz', 'joseph quiz'],
};

export default function GenesisChaptersPage() {
  const genesisBook = getBibleBookBySlug('genesis');
  
  if (!genesisBook) {
    return <div>Book not found</div>;
  }

  // Generate chapter data
  const chapters = Array.from({ length: genesisBook.chapters }, (_, i) => {
    const chapterNumber = i + 1;
    return {
      number: chapterNumber,
      title: getChapter<PERSON>itle(chapterNumber),
      description: getChapterDescription(chapterNumber),
      keyThemes: getChapterThemes(chapterNumber),
      estimatedTime: '8-12 minutes',
      difficulty: getChapterDifficulty(chapterNumber),
      slug: `genesis-${chapterNumber}-quiz`
    };
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Genesis Chapter Quizzes
            </h1>
            <p className="text-xl md:text-2xl mb-6 text-blue-100">
              Test your knowledge of all 50 chapters in the book of beginnings
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link 
                href="/genesis-quiz"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Take Full Genesis Quiz (50 Questions)
              </Link>
              <Link 
                href="/bible-quizzes"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Browse All Bible Quizzes
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Introduction */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-8 items-center">
            <div className="lg:w-2/3">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Explore Genesis Chapter by Chapter
              </h2>
              <p className="text-lg text-gray-700 mb-4">
                Genesis, the book of beginnings, contains some of the most foundational stories in Scripture. 
                From creation to the patriarchs, each chapter reveals God's plan for humanity. Test your 
                knowledge with our comprehensive chapter-by-chapter quizzes.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">50</div>
                  <div className="text-sm text-gray-600">Chapters</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">16-20</div>
                  <div className="text-sm text-gray-600">Questions Each</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">8-12</div>
                  <div className="text-sm text-gray-600">Minutes Each</div>
                </div>
              </div>
            </div>
            <div className="lg:w-1/3">
              <Image
                src="/images/genesis-creation.jpg"
                alt="Genesis Creation"
                width={400}
                height={300}
                className="rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>

        {/* Chapter Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {chapters.map((chapter) => (
            <div key={chapter.number} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-gray-900">
                    Chapter {chapter.number}
                  </h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    chapter.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                    chapter.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {chapter.difficulty}
                  </span>
                </div>
                
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  {chapter.title}
                </h4>
                
                <p className="text-gray-600 text-sm mb-4">
                  {chapter.description}
                </p>
                
                <div className="mb-4">
                  <div className="text-xs font-medium text-gray-500 mb-2">Key Themes:</div>
                  <div className="flex flex-wrap gap-1">
                    {chapter.keyThemes.map((theme, index) => (
                      <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                        {theme}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>⏱️ {chapter.estimatedTime}</span>
                  <span>📝 16-20 questions</span>
                </div>
                
                <Link
                  href={`/${chapter.slug}`}
                  className="block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  Take Chapter {chapter.number} Quiz
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-12 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready for the Ultimate Challenge?</h2>
          <p className="text-xl mb-6">
            Test your comprehensive knowledge of Genesis with our complete book quiz
          </p>
          <Link
            href="/genesis-quiz"
            className="bg-white text-indigo-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors inline-block"
          >
            Take the Full Genesis Quiz (50 Questions)
          </Link>
        </div>
      </div>
    </div>
  );
}

// Helper functions for chapter data
function getChapterTitle(chapter: number): string {
  const titles: { [key: number]: string } = {
    1: "The Creation",
    2: "The Garden of Eden",
    3: "The Fall",
    4: "Cain and Abel",
    5: "From Adam to Noah",
    6: "Noah and the Flood",
    7: "The Flood Begins",
    8: "The Flood Ends",
    9: "God's Covenant with Noah",
    10: "The Table of Nations",
    11: "The Tower of Babel",
    12: "The Call of Abraham",
    13: "Abraham and Lot Separate",
    14: "Abraham Rescues Lot",
    15: "God's Covenant with Abraham",
    16: "Hagar and Ishmael",
    17: "The Covenant of Circumcision",
    18: "The Lord Visits Abraham",
    19: "Sodom and Gomorrah",
    20: "Abraham and Abimelech",
    21: "The Birth of Isaac",
    22: "Abraham Tested",
    23: "The Death of Sarah",
    24: "Isaac and Rebekah",
    25: "The Death of Abraham",
    26: "Isaac and Abimelech",
    27: "Jacob Gets Isaac's Blessing",
    28: "Jacob's Dream at Bethel",
    29: "Jacob Marries Leah and Rachel",
    30: "Jacob's Children",
    // Add more as needed...
  };
  return titles[chapter] || `Chapter ${chapter}`;
}

function getChapterDescription(chapter: number): string {
  const descriptions: { [key: number]: string } = {
    1: "God creates the heavens, earth, and all living things in six days.",
    2: "God forms man from dust and creates the Garden of Eden.",
    3: "The serpent tempts Eve, and sin enters the world.",
    4: "Cain kills his brother Abel in jealousy.",
    5: "The genealogy from Adam to Noah spanning many generations.",
    // Add more as needed...
  };
  return descriptions[chapter] || `Study the key events and teachings in Genesis chapter ${chapter}.`;
}

function getChapterThemes(chapter: number): string[] {
  const themes: { [key: number]: string[] } = {
    1: ["Creation", "God's Power", "Order"],
    2: ["Garden of Eden", "Marriage", "Rest"],
    3: ["Sin", "Consequences", "Promise"],
    4: ["Jealousy", "Murder", "Consequences"],
    5: ["Genealogy", "Longevity", "Death"],
    // Add more as needed...
  };
  return themes[chapter] || ["Faith", "Obedience", "God's Plan"];
}

function getChapterDifficulty(chapter: number): 'easy' | 'medium' | 'hard' {
  // First few chapters are easier, genealogies are harder
  if (chapter <= 3 || chapter === 6 || chapter === 7 || chapter === 8) return 'easy';
  if (chapter === 5 || chapter === 10 || chapter === 11 || chapter >= 36) return 'hard';
  return 'medium';
}
