'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BQ</span>
              </div>
              <span className="text-xl font-bold text-gray-900">SalvationCall</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              Home
            </Link>
            
            {/* Bible Quizzes Dropdown */}
            <div className="relative group">
              <button className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors flex items-center">
                Bible Quizzes
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  <Link href="/bible-quizzes" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">All Bible Quizzes</Link>
                  <Link href="/old-testament-quizzes" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Old Testament</Link>
                  <Link href="/new-testament-quizzes" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">New Testament</Link>
                  <Link href="/bible-quiz-difficulty" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">By Difficulty</Link>
                  <Link href="/kids-bible-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Kids & Youth</Link>
                  <Link href="/popular-bible-quizzes" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Popular Quizzes</Link>
                </div>
              </div>
            </div>

            {/* Study Guides Dropdown */}
            <div className="relative group">
              <button className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors flex items-center">
                Study Guides
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  <Link href="/bible-study-guides" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">All Study Guides</Link>
                  <Link href="/bible-book-studies" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Book Studies</Link>
                  <Link href="/bible-chapter-studies" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Chapter Studies</Link>
                  <Link href="/bible-reading-plans" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Reading Plans</Link>
                </div>
              </div>
            </div>

            {/* Characters Dropdown */}
            <div className="relative group">
              <button className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors flex items-center">
                Characters
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  <Link href="/bible-characters" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">All Characters</Link>
                  <Link href="/old-testament-characters" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Old Testament</Link>
                  <Link href="/new-testament-characters" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">New Testament</Link>
                  <Link href="/women-bible-characters" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">Women in Bible</Link>
                </div>
              </div>
            </div>

            <Link href="/about" className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              About
            </Link>
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 p-2"
              aria-label="Toggle mobile menu"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu - CRITICAL: Max 70% screen coverage */}
      {isMobileMenuOpen && (
        <div className="md:hidden fixed inset-0 z-50 flex">
          {/* Backdrop - 30% minimum visibility */}
          <div 
            className="w-[30%] bg-black bg-opacity-25" 
            onClick={closeMobileMenu}
            aria-hidden="true"
          />
          
          {/* Menu Panel - Max 70% width */}
          <div className="w-[70%] bg-white shadow-xl overflow-y-auto">
            <div className="px-4 pt-4 pb-6">
              {/* Close button */}
              <div className="flex justify-between items-center mb-6">
                <span className="text-lg font-semibold text-gray-900">Menu</span>
                <button
                  onClick={closeMobileMenu}
                  className="text-gray-500 hover:text-gray-700 p-2"
                  aria-label="Close menu"
                >
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Mobile menu items */}
              <nav className="space-y-1">
                <Link 
                  href="/" 
                  className="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                  onClick={closeMobileMenu}
                >
                  🏠 Home
                </Link>
                
                <div className="space-y-1">
                  <div className="px-3 py-2 text-sm font-semibold text-gray-500 uppercase tracking-wider">Bible Quizzes</div>
                  <Link href="/bible-quizzes" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>📖 All Bible Quizzes</Link>
                  <Link href="/old-testament-quizzes" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>📜 Old Testament</Link>
                  <Link href="/new-testament-quizzes" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>✝️ New Testament</Link>
                  <Link href="/bible-quiz-difficulty" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>🎯 By Difficulty</Link>
                  <Link href="/kids-bible-quiz" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>👶 Kids & Youth</Link>
                </div>

                <div className="space-y-1">
                  <div className="px-3 py-2 text-sm font-semibold text-gray-500 uppercase tracking-wider">Study Resources</div>
                  <Link href="/bible-study-guides" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>📚 Study Guides</Link>
                  <Link href="/bible-characters" className="block px-6 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" onClick={closeMobileMenu}>👥 Characters</Link>
                </div>

                <Link 
                  href="/about" 
                  className="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                  onClick={closeMobileMenu}
                >
                  ℹ️ About
                </Link>
              </nav>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
