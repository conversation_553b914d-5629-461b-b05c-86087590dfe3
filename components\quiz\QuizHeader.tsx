import Image from 'next/image';
import Link from 'next/link';
import { Quiz } from '../../lib/types/quiz';

interface QuizHeaderProps {
  quiz: Quiz;
}

const QuizHeader = ({ quiz }: QuizHeaderProps) => {
  return (
    <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center justify-center space-x-2 text-sm text-blue-200">
              <li><Link href="/" className="hover:text-white">Home</Link></li>
              <li>→</li>
              <li><Link href="/bible-quizzes" className="hover:text-white">Bible Quizzes</Link></li>
              <li>→</li>
              {quiz.isBookQuiz && quiz.bookName && (
                <>
                  <li><Link href="/old-testament-quizzes" className="hover:text-white">Old Testament</Link></li>
                  <li>→</li>
                </>
              )}
              <li className="text-white font-medium">{quiz.title}</li>
            </ol>
          </nav>

          {/* Main Title */}
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {quiz.title}
          </h1>
          
          <p className="text-xl md:text-2xl mb-6 text-blue-100 max-w-3xl mx-auto">
            {quiz.description}
          </p>

          {/* Quiz Stats */}
          <div className="flex flex-wrap justify-center gap-6 mb-8">
            <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
              <div className="text-2xl font-bold">{quiz.totalQuestions}</div>
              <div className="text-sm text-blue-200">Questions</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
              <div className="text-2xl font-bold">{quiz.estimatedTime}</div>
              <div className="text-sm text-blue-200">Minutes</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
              <div className="text-2xl font-bold capitalize">{quiz.difficulty}</div>
              <div className="text-sm text-blue-200">Difficulty</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
              <div className="text-2xl font-bold">70%</div>
              <div className="text-sm text-blue-200">To Pass</div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {quiz.isBookQuiz && quiz.bookName && (
              <Link 
                href={`/${quiz.bookName.toLowerCase()}-chapters`}
                className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                📖 {quiz.bookName} Chapter Quizzes
              </Link>
            )}
            <Link 
              href="/bible-study-guides"
              className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              📚 Study Guide First
            </Link>
            <Link 
              href="/bible-quizzes"
              className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              🔍 Browse All Quizzes
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizHeader;
