import Link from 'next/link';
import { Quiz, QuizResult } from '../../lib/types/quiz';
import { getPerformanceMessage } from '../../lib/utils/quiz-utils';
import QuizQuestion from './QuizQuestion';

interface QuizResultsProps {
  quiz: Quiz;
  result: QuizResult;
  onRetakeQuiz: () => void;
}

const QuizResults = ({ quiz, result, onRetakeQuiz }: QuizResultsProps) => {
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getGradeBg = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-50 border-green-200';
    if (percentage >= 80) return 'bg-blue-50 border-blue-200';
    if (percentage >= 70) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  return (
    <div id="quiz-results" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      
      {/* Results Header */}
      <div className={`rounded-lg border-2 p-8 mb-8 text-center ${getGradeBg(result.percentage)}`}>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Quiz Complete! 🎉
        </h1>
        
        <div className={`text-6xl font-bold mb-4 ${getGradeColor(result.percentage)}`}>
          {result.percentage}%
        </div>
        
        <p className="text-xl text-gray-700 mb-6">
          {getPerformanceMessage(result.percentage)}
        </p>

        {/* Score Breakdown */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="text-2xl font-bold text-gray-900">{result.score}</div>
            <div className="text-sm text-gray-600">Correct</div>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="text-2xl font-bold text-gray-900">{result.totalQuestions - result.score}</div>
            <div className="text-sm text-gray-600">Incorrect</div>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="text-2xl font-bold text-gray-900">{formatTime(result.timeSpent)}</div>
            <div className="text-sm text-gray-600">Time Taken</div>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="text-2xl font-bold text-gray-900">{result.passed ? '✅' : '❌'}</div>
            <div className="text-sm text-gray-600">{result.passed ? 'Passed' : 'Failed'}</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={onRetakeQuiz}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            🔄 Retake Quiz
          </button>
          <Link
            href="/bible-quizzes"
            className="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors text-center"
          >
            🔍 Browse More Quizzes
          </Link>
        </div>
      </div>

      {/* Internal Links Section - CRITICAL SEO FEATURE */}
      <div className="bg-white rounded-lg shadow-md p-8 mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          📚 Continue Your Bible Study Journey
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* Study Resources */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">📖 Study Resources</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href={`/${quiz.bookName?.toLowerCase()}-study-guide`} className="text-blue-700 hover:text-blue-900 hover:underline">
                  📚 Study {quiz.bookName} Guide First
                </Link>
              </li>
              <li>
                <Link href={`/${quiz.bookName?.toLowerCase()}-summary`} className="text-blue-700 hover:text-blue-900 hover:underline">
                  📝 {quiz.bookName} Chapter Summaries
                </Link>
              </li>
              <li>
                <Link href={`/${quiz.bookName?.toLowerCase()}-key-verses`} className="text-blue-700 hover:text-blue-900 hover:underline">
                  ✨ {quiz.bookName} Key Verses
                </Link>
              </li>
            </ul>
          </div>

          {/* Related Quizzes */}
          <div className="bg-green-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4">🎯 Related Quizzes</h3>
            <ul className="space-y-2 text-sm">
              {quiz.isBookQuiz && quiz.bookName && (
                <li>
                  <Link href={`/${quiz.bookName.toLowerCase()}-chapters`} className="text-green-700 hover:text-green-900 hover:underline">
                    📖 {quiz.bookName} Chapter Quizzes
                  </Link>
                </li>
              )}
              <li>
                <Link href="/old-testament-quizzes" className="text-green-700 hover:text-green-900 hover:underline">
                  📜 More Old Testament Quizzes
                </Link>
              </li>
              <li>
                <Link href="/bible-character-quizzes" className="text-green-700 hover:text-green-900 hover:underline">
                  👥 Bible Character Quizzes
                </Link>
              </li>
            </ul>
          </div>

          {/* Navigation */}
          <div className="bg-purple-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-purple-900 mb-4">🧭 Quick Navigation</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/bible-quizzes" className="text-purple-700 hover:text-purple-900 hover:underline">
                  🏠 Browse All Bible Quizzes
                </Link>
              </li>
              <li>
                <Link href="/popular-bible-quizzes" className="text-purple-700 hover:text-purple-900 hover:underline">
                  🔥 Popular Bible Quizzes
                </Link>
              </li>
              <li>
                <Link href="/daily-bible-quiz" className="text-purple-700 hover:text-purple-900 hover:underline">
                  📅 Daily Bible Quiz
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Character Studies (if applicable) */}
        {quiz.isBookQuiz && quiz.bookName === 'Genesis' && (
          <div className="mt-6 bg-yellow-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-900 mb-4">👥 Genesis Character Studies</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
              <Link href="/abraham-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Abraham Quiz</Link>
              <Link href="/isaac-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Isaac Quiz</Link>
              <Link href="/jacob-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Jacob Quiz</Link>
              <Link href="/joseph-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Joseph Quiz</Link>
              <Link href="/noah-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Noah Quiz</Link>
              <Link href="/moses-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Moses Quiz</Link>
              <Link href="/adam-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Adam Quiz</Link>
              <Link href="/eve-quiz" className="text-yellow-700 hover:text-yellow-900 hover:underline">Eve Quiz</Link>
            </div>
          </div>
        )}
      </div>

      {/* Detailed Results */}
      <div className="bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          📋 Detailed Results & Explanations
        </h2>
        
        <div className="space-y-8">
          {quiz.questions.map((question, index) => {
            const userAnswer = result.userAnswers.find(a => a.questionId === question.id);
            return (
              <QuizQuestion
                key={question.id}
                question={question}
                questionNumber={index + 1}
                userAnswer={userAnswer}
                onAnswerSelect={() => {}} // No interaction in results mode
                showResults={true}
              />
            );
          })}
        </div>
      </div>

      {/* Share Results */}
      <div className="mt-8 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Share Your Results! 🎉</h2>
        <p className="text-blue-100 mb-6">
          I scored {result.percentage}% on the {quiz.title}! Test your Bible knowledge too.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
            📱 Share on Facebook
          </button>
          <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
            🐦 Share on Twitter
          </button>
          <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
            📧 Email Results
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuizResults;
