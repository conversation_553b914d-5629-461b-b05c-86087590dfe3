import { QuizQuestion as QuizQuestionType, UserAnswer } from '../../lib/types/quiz';

interface QuizQuestionProps {
  question: QuizQuestionType;
  questionNumber: number;
  userAnswer?: UserAnswer;
  onAnswerSelect: (questionId: string, answerId: string) => void;
  showResults: boolean;
}

const QuizQuestion = ({ 
  question, 
  questionNumber, 
  userAnswer, 
  onAnswerSelect, 
  showResults 
}: QuizQuestionProps) => {
  
  const handleAnswerClick = (answerId: string) => {
    if (!showResults) {
      onAnswerSelect(question.id, answerId);
    }
  };

  const getAnswerClassName = (answerId: string) => {
    const baseClasses = "w-full text-left p-3 rounded-md border transition-all duration-200";

    if (showResults) {
      // Show results mode
      if (answerId === question.correctAnswerId) {
        return `${baseClasses} bg-green-50 border-green-300 text-green-800`;
      } else if (userAnswer && userAnswer.answerId === answerId && !userAnswer.isCorrect) {
        return `${baseClasses} bg-red-50 border-red-300 text-red-800`;
      } else {
        return `${baseClasses} bg-gray-50 border-gray-200 text-gray-600`;
      }
    } else {
      // Interactive mode - Clean blue buttons like in the image
      if (userAnswer && userAnswer.answerId === answerId) {
        return `${baseClasses} bg-blue-600 border-blue-600 text-white`;
      } else {
        return `${baseClasses} bg-blue-500 border-blue-500 text-white hover:bg-blue-600 hover:border-blue-600 cursor-pointer`;
      }
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6" id={`question-${question.id}`}>
      {/* Question Header - Clean and Simple */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {questionNumber}. {question.question}
        </h3>

        {/* Bible Reference */}
        {question.bibleReference && (
          <p className="text-sm text-gray-600 mb-3">
            {question.bibleReference}
          </p>
        )}

        {/* Status indicator for answered questions */}
        {userAnswer && !showResults && (
          <div className="flex items-center space-x-2 mb-3">
            <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
              ✓ Answered
            </span>
          </div>
        )}
      </div>

      {/* Answer Options - Clean Button Style */}
      <div className="space-y-2">
        {question.questionType === 'fill-blank' ? (
          <div>
            <input
              type="text"
              placeholder="Type your answer here..."
              className="w-full p-3 border border-gray-300 rounded-md focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              onChange={(e) => handleAnswerClick(e.target.value)}
              disabled={showResults}
            />
          </div>
        ) : (
          question.answers.map((answer) => (
            <button
              key={answer.id}
              onClick={() => handleAnswerClick(answer.id)}
              className={getAnswerClassName(answer.id)}
              disabled={showResults}
            >
              <div className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-6 h-6 rounded border border-current flex items-center justify-center text-sm font-medium">
                  {question.questionType === 'true-false' ?
                    (answer.id === 'true' ? 'T' : 'F') :
                    answer.id.toUpperCase()
                  }
                </span>
                <span className="flex-1 text-left">{answer.text}</span>
                {showResults && answer.id === question.correctAnswerId && (
                  <span className="text-green-600 font-bold">✓</span>
                )}
                {showResults && userAnswer && userAnswer.answerId === answer.id && !userAnswer.isCorrect && (
                  <span className="text-red-600 font-bold">✗</span>
                )}
              </div>
            </button>
          ))
        )}
      </div>

      {/* Explanation (shown in results mode) */}
      {showResults && question.explanation && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
          <h4 className="font-semibold text-blue-900 mb-2">💡 Explanation:</h4>
          <p className="text-blue-800 text-sm">{question.explanation}</p>
        </div>
      )}

      {/* Question Type Indicator */}
      <div className="mt-4 flex items-center justify-between text-xs text-gray-500">
        <span>
          Type: {question.questionType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </span>
        {question.bibleReference && (
          <span>
            Reference: {question.bibleReference}
          </span>
        )}
      </div>
    </div>
  );
};

export default QuizQuestion;
