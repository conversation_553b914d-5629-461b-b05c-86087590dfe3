import { QuizQuestion as QuizQuestionType, UserAnswer } from '../../lib/types/quiz';

interface QuizQuestionProps {
  question: QuizQuestionType;
  questionNumber: number;
  userAnswer?: UserAnswer;
  onAnswerSelect: (questionId: string, answerId: string) => void;
  showResults: boolean;
}

const QuizQuestion = ({ 
  question, 
  questionNumber, 
  userAnswer, 
  onAnswerSelect, 
  showResults 
}: QuizQuestionProps) => {
  
  const handleAnswerClick = (answerId: string) => {
    if (!showResults) {
      onAnswerSelect(question.id, answerId);
    }
  };

  const getAnswerClassName = (answerId: string) => {
    const baseClasses = "w-full text-left p-4 rounded-lg border-2 transition-all duration-200";
    
    if (showResults) {
      // Show results mode
      if (answerId === question.correctAnswerId) {
        return `${baseClasses} bg-green-100 border-green-500 text-green-800`;
      } else if (userAnswer && userAnswer.answerId === answerId && !userAnswer.isCorrect) {
        return `${baseClasses} bg-red-100 border-red-500 text-red-800`;
      } else {
        return `${baseClasses} bg-gray-50 border-gray-300 text-gray-600`;
      }
    } else {
      // Interactive mode
      if (userAnswer && userAnswer.answerId === answerId) {
        return `${baseClasses} bg-blue-100 border-blue-500 text-blue-800`;
      } else {
        return `${baseClasses} bg-white border-gray-300 text-gray-700 hover:bg-blue-50 hover:border-blue-300 cursor-pointer`;
      }
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6" id={`question-${question.id}`}>
      {/* Question Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
            {questionNumber}
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty)}`}>
            {question.difficulty}
          </span>
          <span className="text-xs text-gray-500">
            {question.points} point{question.points !== 1 ? 's' : ''}
          </span>
        </div>
        
        {userAnswer && !showResults && (
          <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
            ✓ Answered
          </span>
        )}
      </div>

      {/* Question Text */}
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {question.question}
      </h3>

      {/* Bible Reference */}
      {question.bibleReference && (
        <p className="text-sm text-blue-600 mb-4 font-medium">
          📖 {question.bibleReference}
        </p>
      )}

      {/* Answer Options */}
      <div className="space-y-3 mb-4">
        {question.questionType === 'fill-blank' ? (
          <div>
            <input
              type="text"
              placeholder="Type your answer here..."
              className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
              onChange={(e) => handleAnswerClick(e.target.value)}
              disabled={showResults}
            />
          </div>
        ) : (
          question.answers.map((answer) => (
            <button
              key={answer.id}
              onClick={() => handleAnswerClick(answer.id)}
              className={getAnswerClassName(answer.id)}
              disabled={showResults}
            >
              <div className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-xs font-bold">
                  {question.questionType === 'true-false' ? 
                    (answer.id === 'true' ? 'T' : 'F') : 
                    answer.id.toUpperCase()
                  }
                </span>
                <span className="flex-1">{answer.text}</span>
                {showResults && answer.id === question.correctAnswerId && (
                  <span className="text-green-600">✓</span>
                )}
                {showResults && userAnswer && userAnswer.answerId === answer.id && !userAnswer.isCorrect && (
                  <span className="text-red-600">✗</span>
                )}
              </div>
            </button>
          ))
        )}
      </div>

      {/* Explanation (shown in results mode) */}
      {showResults && question.explanation && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
          <h4 className="font-semibold text-blue-900 mb-2">💡 Explanation:</h4>
          <p className="text-blue-800 text-sm">{question.explanation}</p>
        </div>
      )}

      {/* Question Type Indicator */}
      <div className="mt-4 flex items-center justify-between text-xs text-gray-500">
        <span>
          Type: {question.questionType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </span>
        {question.bibleReference && (
          <span>
            Reference: {question.bibleReference}
          </span>
        )}
      </div>
    </div>
  );
};

export default QuizQuestion;
